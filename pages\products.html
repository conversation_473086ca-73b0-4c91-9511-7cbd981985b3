<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Products - Creative Hydraulics - Complete Hydraulic Solutions</title>
    <link rel="icon" type="image/png" href="../assets/logos/logo.png">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Outfit:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Jura:wght@300..700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Michroma&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'display': ['Outfit', 'sans-serif'],
                        'Jura': ['Jura', 'sans-serif'],
                        'Michroma': ['Michroma', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        .hero-gradient {
            background: linear-gradient(rgba(0, 48, 100, 0.7), rgba(0, 48, 100, 0.9));
        }

        .nav-link {
            position: relative;
        }

        .nav-link {
            color: #374151; /* Default text color */
        }

        .nav-link-active {
            color: #D22B2B !important; /* Red text for active page */
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background-color: #2563eb; /* Blue underline */
            transition: all 0.3s ease-in-out;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .top-bar-link {
            transition: all 0.3s ease;
        }

        .top-bar-link:hover {
            color: #60a5fa;
        }

        .top-bar-separator {
            width: 1px;
            height: 16px;
            background: rgba(255, 255, 255, 0.2);
            margin: 0 1.5rem;
        }

        .social-icon {
            @apply w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300;
        }

        .social-icon:hover {
            @apply bg-white/10 transform -translate-y-0.5;
        }

        .nav-dropdown {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            pointer-events: none;
            top: 100%;
            right: 0;
            z-index: 50;
        }

        .nav-dropdown.show,
        .dropdown-parent:hover .nav-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            pointer-events: auto;
        }

        .dropdown-item {
            transition: all 0.2s ease;
            position: relative;
        }

        .dropdown-item:hover {
            background-color: #f1f5f9;
            color: #0047AB;
            padding-left: 20px;
        }

        .dropdown-item:hover::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: #0047AB;
        }

        /* Create a bridge area to prevent dropdown from disappearing */
        .dropdown-parent::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            height: 8px;
            background: transparent;
            z-index: 49;
        }

        /* Ensure dropdown stays visible when hovering over it or the bridge */
        .dropdown-parent:hover .nav-dropdown,
        .nav-dropdown:hover {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            pointer-events: auto;
        }

        /* Improved dropdown positioning and styling */
        .dropdown-parent {
            position: relative;
        }

        .nav-dropdown {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .mobile-menu-container {
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.98));
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }

            .mobile-menu-item {
                position: relative;
                transition: all 0.3s ease;
            }

            .mobile-menu-item::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 1px;
                background: linear-gradient(to right, transparent, rgba(30, 64, 175, 0.1), transparent);
            }

            .mobile-icon {
                transition: all 0.3s ease;
            }

            .mobile-icon:hover {
                transform: translateY(-1px);
            }
        }

        html {
            scroll-behavior: smooth;
        }

        .scroll-mt {
            scroll-margin-top: 6rem;
        }

        .font-Michroma { font-family: 'Michroma', sans-serif; }
        .font-Jura { font-family: 'Jura', sans-serif; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
        }
    </style>
</head>

<body class="font-body">
    <!-- Top Info Bar -->
    <!-- <div class="bg-[#111827]">
        <div class="container mx-auto font-Jura">
            <div class="hidden md:flex justify-between items-center py-1 px-6">
                
                <div class="flex items-center space-x-6">
                    <a href="tel:09449024839"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide">
                        <i class="fas fa-phone mr-2.5 text-[#D22B2B] group-hover:rotate-12 transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5">+91 ************</span>
                    </a>
                    <a href="tel:+91 ************"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide">
                        <i class="fas fa-phone mr-2.5 text-[#D22B2B] group-hover:rotate-12 transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5">+91 ************</span>
                    </a>
                    <div class="top-bar-separator"></div>
                    <a href="mailto:<EMAIL>"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide">
                        <i class="fas fa-envelope mr-2.5 text-[#D22B2B] group-hover:scale-110 transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5"><EMAIL></span>
                    </a>
                </div>

                
                <div class="flex items-center">
                    <a href="#location"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide mr-8">
                        <i
                            class="fas fa-map-marker-alt mr-2 text-[#D22B2B] group-hover:bounce transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5">Our Location</span>
                    </a>
                    <div class="top-bar-separator"></div>
                    <div class="flex items-center space-x-3 ml-8">
                        <a href="#" class="social-icon text-white/90 hover:text-white" aria-label="LinkedIn">
                            <i class="fab fa-linkedin text-[15px]"></i>
                        </a>
                        <a href="#" class="social-icon text-white/90 hover:text-white" aria-label="Twitter">
                            <i class="fab fa-twitter text-[15px]"></i>
                        </a>
                        <a href="#" class="social-icon text-white/90 hover:text-white" aria-label="Facebook">
                            <i class="fab fa-facebook text-[15px]"></i>
                        </a>
                    </div>
                </div>
            </div>

            
            <div class="flex md:hidden justify-between items-center py-2.5 px-4">
                <a href="mailto:<EMAIL>"
                    class="flex items-center text-white/90 hover:text-white group tracking-wide">
                    <i class="fas fa-envelope mr-1.5 text-[#D22B2B] group-hover:scale-110 transition-transform"></i>
                    <span class="text-sm font-medium -translate-y-0.5"><EMAIL></span>
                </a>
                <div class="flex items-center space-x-4">
                    <a href="#"
                        class="w-7 h-7 flex items-center justify-center text-white/90 hover:text-white mobile-icon"
                        aria-label="LinkedIn">
                        <i class="fab fa-linkedin text-[15px]"></i>
                    </a>
                    <a href="#"
                        class="w-7 h-7 flex items-center justify-center text-white/90 hover:text-white mobile-icon"
                        aria-label="Twitter">
                        <i class="fab fa-twitter text-[15px]"></i>
                    </a>
                    <a href="#"
                        class="w-7 h-7 flex items-center justify-center text-white/90 hover:text-white mobile-icon"
                        aria-label="Facebook">
                        <i class="fab fa-facebook text-[15px]"></i>
                    </a>
                </div>
            </div>
        </div>
    </div> -->

    <!-- Main Navigation -->
    <nav class="bg-[#ffffff] shadow-lg sticky top-0 z-50 transition-shadow duration-300">
        <div class="container mx-auto">
            <div class="flex justify-between items-center h-20 px-4">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="../index.html" class="flex items-center space-x-2">
                        <div
                            class="w-16 h-16 md:w-20 md:h-20 flex items-center justify-center shadow-lg">
                            <img src="../assets/logos/logo.png" alt="Creative Hydraulics Logo" class="w-14 h-14 md:w-18 md:h-18 object-contain" />
                        </div>
                        <div class="flex flex-col translate-y-0.5">
                            <span
                                class="text-lg md:text-xl font-Michroma font-bold bg-gradient-to-r from-blue-900 to-blue-700 bg-clip-text text-transparent tracking-wide">
                                Creative Hydraulics
                            </span>
                            <span
                                class="text-xs mx-0.5 -translate-y-1 font-Jura  md:text-sm text-gray-600 tracking-wider">Complete Pressure Testing Solutions</span>
                        </div>
                    </a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-2">
                    <a href="../index.html"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Home</a>
                    
                    <a href="about.html"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">About Us</a>
                    <a href="../index.html#certifications"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Certifications</a>
                    
                    <!-- Products Dropdown -->
                    <div class="dropdown-parent">
                        <button class="nav-link nav-link-active px-3 py-2 font-Jura font-bold tracking-wide text-[15px] flex items-center">
                            Products
                            <i class="fas fa-chevron-down ml-1 text-xs transition-transform dropdown-parent:hover:rotate-180"></i>
                        </button>
                        <div class="nav-dropdown absolute w-[650px] rounded-xl overflow-hidden">
                            <div class="py-4">
                                <!-- View All Products Link -->
                                <div class="px-5 py-3 border-b border-gray-100 mb-3">
                                    <a href="products.html" class="flex items-center text-gray-700 font-Jura font-medium text-sm hover:bg-blue-50 rounded-lg p-2 transition-all duration-200">
                                        <div class="w-8 h-8 bg-[#D22B2B]/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-th-large text-[#D22B2B] text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">View All Products</div>
                                            <div class="text-xs text-gray-500">Complete product overview</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="grid grid-cols-2 gap-0">
                                    <!-- Left Column -->
                                    <div class="space-y-1">
                                        <a href="manual.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                                <i class="fas fa-hand-paper text-blue-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900">Manual Systems</div>
                                                <div class="text-xs text-gray-500">Precision manual control</div>
                                            </div>
                                        </a>
                                        <a href="semi.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                                <i class="fas fa-cogs text-blue-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900">Semi-Automatic</div>
                                                <div class="text-xs text-gray-500">PLC controlled systems</div>
                                            </div>
                                        </a>
                                        <a href="auto.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                                <i class="fas fa-robot text-blue-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900">Fully Automatic</div>
                                                <div class="text-xs text-gray-500">AI-driven automation</div>
                                            </div>
                                        </a>
                                    <a href="hts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-cog text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Horizontal Test Stand</div>
                                                <div class="text-xs text-gray-500">HTS - Pressure testing</div>
                                        </div>
                                    </a>
                                    <a href="vts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-arrows-alt-v text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Vertical Test Stand</div>
                                                <div class="text-xs text-gray-500">VTS - Vertical testing</div>
                                        </div>
                                    </a>
                                    </div>
                                    
                                    <!-- Right Column -->
                                    <div class="space-y-1">
                                    <a href="mshts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-sitemap text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                                <div class="font-semibold text-gray-900">Multi-Station Test</div>
                                                <div class="text-xs text-gray-500">MSHTS - Multi-station</div>
                                        </div>
                                    </a>
                                    <a href="fsts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-fire-extinguisher text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Fire Safe Test Stand</div>
                                                <div class="text-xs text-gray-500">Fire safety testing</div>
                                        </div>
                                    </a>
                                    <a href="cts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-snowflake text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Cryogenic Test Stand</div>
                                            <div class="text-xs text-gray-500">Low temperature testing</div>
                                        </div>
                                    </a>
                                    <a href="fet.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-search text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Fugitive Emission Test</div>
                                                <div class="text-xs text-gray-500">FET - Emission testing</div>
                                        </div>
                                    </a>
                                    <a href="hp.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-compress-arrows-alt text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Hydraulic Presses</div>
                                                <div class="text-xs text-gray-500">Industrial pressing</div>
                                        </div>
                                    </a>
                                    <a href="spm.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-tools text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Special Purpose Machine</div>
                                            <div class="text-xs text-gray-500">SPM - Custom solutions</div>
                                        </div>
                                    </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <a href="infrastructure.html"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Infrastructure</a>
                    <a href="contact.html"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Contact Us</a>
                </div>

                <!-- custom build -->

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="p-2 rounded-xl hover:bg-blue-50/80 transition-colors group">
                        <svg class="h-6 w-6 text-blue-900 group-hover:text-blue-700 transition-colors" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden font-Jura md:hidden">
                <div class="fixed inset-0 bg-black/20 z-40 transition-opacity duration-300 opacity-0"
                    id="mobile-menu-backdrop"></div>
                <div class="fixed inset-y-0 right-0 w-[280px] bg-white shadow-2xl z-50 transition-transform duration-300 transform translate-x-full mobile-menu-container flex flex-col"
                    id="mobile-menu-content">
                    <!-- Mobile Menu Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-100 flex-shrink-0">
                        <span class="text-lg font-display font-semibold text-blue-900">Menu</span>
                        <button id="mobile-menu-close" class="p-2 rounded-xl hover:bg-blue-50/80 transition-colors">
                            <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile Menu Items - Scrollable Area -->
                    <div class="flex-1 overflow-y-auto py-3" style="max-height: calc(100vh - 180px);">
                        <a href="../index.html"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-gray-800 hover:bg-blue-50/50">
                            <i class="fas fa-home w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Home</span>
                        </a>
                        
                        <a href="about.html"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-gray-800 hover:bg-blue-50/50">
                            <i class="fas fa-info-circle w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">About Us</span>
                        </a>
                        <a href="../index.html#certifications"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-gray-800 hover:bg-blue-50/50">
                            <i class="fas fa-certificate w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Certifications</span>
                        </a>
                        
                        <!-- Mobile Products Dropdown -->
                        <div class="mobile-menu-item">
                            <button class="flex items-center justify-between w-full px-6 py-3.5 text-blue-900 bg-blue-50/50" onclick="toggleMobileDropdown()">
                                <div class="flex items-center">
                            <i class="fas fa-cube w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Products</span>
                                </div>
                                <i class="fas fa-chevron-down text-gray-500 transition-transform rotate-180" id="mobileDropdownIcon"></i>
                            </button>
                            <div class="bg-gray-50 border-l-2 border-blue-200 ml-6" id="mobileProductsDropdown">
                                <a href="products.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm border-b border-gray-200">
                                    <i class="fas fa-th-large text-[#D22B2B] mr-2 w-4"></i>
                                    View All Products
                                </a>
                                <a href="manual.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-hand-paper text-blue-600 mr-2 w-4"></i>
                                    Manual Systems
                                </a>
                                <a href="semi.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-cogs text-blue-600 mr-2 w-4"></i>
                                    Semi-Automatic Systems
                                </a>
                                <a href="auto.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-robot text-blue-600 mr-2 w-4"></i>
                                    Fully Automatic Systems
                                </a>
                                <a href="hts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-cog text-blue-600 mr-2 w-4"></i>
                                    Horizontal Test Stand (HTS)
                                </a>
                                <a href="vts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-arrows-alt-v text-blue-600 mr-2 w-4"></i>
                                    Vertical Test Stand (VTS)
                                </a>
                                <a href="mshts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-sitemap text-blue-600 mr-2 w-4"></i>
                                    Multi-Station Test Stand (MSHTS)
                                </a>
                                <a href="fsts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-fire-extinguisher text-blue-600 mr-2 w-4"></i>
                                    Fire Safe Test Stand
                                </a>
                                <a href="cts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-snowflake text-blue-600 mr-2 w-4"></i>
                                    Cryogenic Test Stand
                                </a>
                                <a href="fet.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-search text-blue-600 mr-2 w-4"></i>
                                    Fugitive Emission Test (FET)
                                </a>
                                <a href="hp.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-compress-arrows-alt text-blue-600 mr-2 w-4"></i>
                                    Hydraulic Presses
                                </a>
                                <a href="spm.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-tools text-blue-600 mr-2 w-4"></i>
                                    Special Purpose Machine (SPM)
                                </a>
                            </div>
                        </div>
                        
                        <a href="infrastructure.html"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-gray-800 hover:bg-blue-50/50">
                            <i class="fas fa-building w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Infrastructure</span>
                        </a>
                    </div>

                    <!-- Mobile Menu Footer -->
                    <div class="flex-shrink-0 p-4 border-t border-gray-100">
                        <a href="contact.html"
                            class="block text-center bg-gradient-to-r from-blue-900 to-blue-700 text-white py-3.5 px-6 rounded-xl hover:shadow-lg transition-all font-Jura font-semibold tracking-wide">
                            Contact Us
                        </a>
                        <div class="mt-4 flex items-center justify-center space-x-4 py-2">
                            <a href="mailto:<EMAIL>"
                                class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-envelope text-lg"></i>
                            </a>
                            <div class="h-4 w-px bg-gray-200"></div>
                            <a href="tel:09449024839" class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-phone text-lg"></i>
                            </a>
                            <div class="h-4 w-px bg-gray-200"></div>
                            <a href="tel:+918971587348" class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-phone text-lg"></i>
                            </a>
                            <div class="h-4 w-px bg-gray-200"></div>
                            <a href="#location" class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-map-marker-alt text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="relative min-h-[30vh] sm:min-h-[35vh] md:min-h-[40vh] lg:min-h-[45vh] xl:min-h-[50vh] overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <!-- Background Image -->
        <div class="absolute inset-0 z-0">
            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Manual.png" 
                 alt="Creative Hydraulics Products" 
                 class="w-full h-full object-cover opacity-20">
            <div class="absolute inset-0 bg-black/70"></div>
        </div>

        <!-- Content -->
        <div class="relative min-h-[30vh] sm:min-h-[35vh] md:min-h-[40vh] lg:min-h-[45vh] xl:min-h-[50vh] flex items-center z-10">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center max-w-6xl mx-auto space-y-6 lg:space-y-0">
                    <!-- Left Content -->
                    <div class="text-left text-white space-y-3 sm:space-y-4 flex-1">
                        <div class="w-8 sm:w-10 md:w-12 h-0.5 bg-[#D22B2B] rounded-full"></div>
                        <h1 class="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-Michroma font-bold animate-fadeInUp opacity-0 leading-tight"
                            style="animation-delay: 0.1s">
                            Our <span class="text-gray-300">Products</span>
                        </h1>
                        <p class="text-xs sm:text-sm md:text-base lg:text-lg font-Jura animate-fadeInUp opacity-0 text-gray-300 max-w-lg lg:max-w-xl leading-relaxed"
                            style="animation-delay: 0.3s">
                            <strong class="text-white">Complete range</strong> of precision hydraulic equipment and testing solutions, engineered for <strong class="text-[#D22B2B]">industrial excellence</strong>.
                        </p>
                    </div>
                    
                    <!-- Right Buttons -->
                    <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 animate-fadeInUp opacity-0 lg:ml-8 w-full sm:w-auto"
                        style="animation-delay: 0.5s">
                        <a href="contact.html"
                            class="bg-[#D22B2B] hover:bg-[#B02020] text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg text-xs sm:text-sm font-Jura font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg whitespace-nowrap text-center">
                            Get Quote
                        </a>
                        <a href="../index.html#products"
                            class="bg-transparent border border-gray-400/80 hover:bg-gray-400 hover:text-gray-900 text-gray-300 px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg text-xs sm:text-sm font-Jura font-medium transition-all duration-300 hover:scale-105 whitespace-nowrap text-center">
                            View All
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Overview Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <span class="text-[#D22B2B] font-bold tracking-wider text-sm mb-2 block font-Jura">OUR PRODUCT RANGE</span>
                <h2 class="text-3xl font-Michroma font-bold text-gray-900 mb-3">Complete Hydraulic Solutions</h2>
                <div class="w-20 h-0.5 bg-[#D22B2B] rounded-full mx-auto"></div>
                <p class="text-gray-600 font-Jura mt-6 max-w-3xl mx-auto">
                    From precision testing equipment to industrial hydraulic presses, we offer a comprehensive range of solutions designed to meet the most demanding industrial requirements.
                </p>
            </div>

            <div class="max-w-7xl mx-auto">
                <!-- Products Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Manual Systems -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Manual.png" 
                                 alt="Manual Systems" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">Manual</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Manual Systems</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                High-performance manual hydraulic systems designed for optimal control and precision in demanding industrial applications.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Precision Control</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Robust Design</span>
                            </div>
                            <a href="manual.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Semi-Automatic Systems -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Semi_aut.png" 
                                 alt="Semi-Automatic Systems" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">Semi-Auto</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Semi-Automatic Systems</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Precision-engineered semi-automatic hydraulic systems for reliable force transmission in manufacturing processes.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">PLC Controlled</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Automated Features</span>
                            </div>
                            <a href="semi.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Fully Automatic Systems -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Fully_aut.png" 
                                 alt="Fully Automatic Systems" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">Automatic</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Fully Automatic Systems</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Advanced fully automatic hydraulic flow control solutions for precise system regulation with intelligent control systems.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">AI-Driven</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Real-time Monitoring</span>
                            </div>
                            <a href="auto.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Special Purpose Machine -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/SPM.png" 
                                 alt="Special Purpose Machine" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">SPM</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Special Purpose Machine</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Custom-designed machines built to perform specific tasks in manufacturing, production, or testing environments.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Custom Solutions</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Tailored Design</span>
                            </div>
                            <a href="spm.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Multi-Station Test Stand -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/MultiStation.png" 
                                 alt="Multi-Station Test Stand" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">Testing</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Multi-Station Test Stand</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Fixed hydrostatic testing setup with multiple test bays for conducting pressure tests on various components.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Multi-Bay</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Advanced Automation</span>
                            </div>
                            <a href="mshts.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Hydraulic Presses -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Hydraulic_press.png" 
                                 alt="Hydraulic Presses" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">Pressing</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Hydraulic Presses</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                High-performance hydraulic presses engineered for precision, durability, and versatility in industrial applications.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">High Precision</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Industrial Grade</span>
                            </div>
                            <a href="hp.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Additional Testing Equipment Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <span class="text-[#D22B2B] font-bold tracking-wider text-sm mb-2 block font-Jura">TESTING SOLUTIONS</span>
                <h2 class="text-3xl font-Michroma font-bold text-gray-900 mb-3">Specialized Testing Equipment</h2>
                <div class="w-20 h-0.5 bg-[#D22B2B] rounded-full mx-auto"></div>
                <p class="text-gray-600 font-Jura mt-6 max-w-3xl mx-auto">
                    Comprehensive range of testing equipment designed for specific industrial applications and compliance requirements.
                </p>
            </div>

            <div class="max-w-7xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Horizontal Test Stand -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Hydraulic_press.png" 
                                 alt="Horizontal Test Stand" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">HTS</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Horizontal Test Stand</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Precision horizontal testing equipment for comprehensive pressure testing of valves and components.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Horizontal Testing</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Valve Testing</span>
                            </div>
                            <a href="hts.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Vertical Test Stand -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/vts.png" 
                                 alt="Vertical Test Stand" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">VTS</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Vertical Test Stand</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Advanced vertical testing systems for specialized component testing and quality assurance.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Vertical Testing</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Quality Assurance</span>
                            </div>
                            <a href="vts.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Fire Safe Test Stand -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/fsts.png" 
                                 alt="Fire Safe Test Stand" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#D22B2B] text-white text-sm font-Jura font-medium rounded-full">Fire Safe</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Fire Safe Test Stand</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Specialized testing equipment for fire safety compliance and emergency system validation.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Fire Safety</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Compliance Testing</span>
                            </div>
                            <a href="fsts.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Cryogenic Test Stand -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/cet.png" 
                                 alt="Cryogenic Test Stand" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">Cryogenic</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Cryogenic Test Stand</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Low-temperature testing equipment for cryogenic applications and extreme environment testing.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Low Temperature</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Extreme Testing</span>
                            </div>
                            <a href="cts.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Fugitive Emission Test -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/fet.png" 
                                 alt="Fugitive Emission Test" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">FET</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Fugitive Emission Test</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                Advanced emission testing equipment for environmental compliance and leak detection.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Emission Testing</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Leak Detection</span>
                            </div>
                            <a href="fet.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Multi-Station Test Stand -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/MultiStation.png" 
                                 alt="Multi-Station Test Stand" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#D22B2B] text-white text-sm font-Jura font-medium rounded-full">MSHTS</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Multi-Station Test Stand</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                High-capacity multi-station testing equipment for simultaneous testing of multiple components.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Multi-Station</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">High Capacity</span>
                            </div>
                            <a href="mshts.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Hydraulic Presses -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Hydraulic_press.png" 
                                 alt="Hydraulic Presses" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium rounded-full">HP</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">Hydraulic Presses</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                High-performance hydraulic presses engineered for precision, durability, and versatility in industrial applications.
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Industrial Pressing</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Precision Control</span>
                            </div>
                            <a href="hp.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Learn More <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Contact CTA -->
                    <div class="bg-gradient-to-br from-[#0047AB] to-[#003A8C] rounded-xl shadow-lg overflow-hidden border-2 border-[#0047AB]/20 hover:border-[#0047AB]/40 transition-all duration-300">
                        <div class="h-full flex flex-col justify-center p-6 text-center text-white">
                            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cogs text-white text-xl"></i>
                            </div>
                            <h3 class="text-xl font-Michroma font-bold mb-3">Custom Solutions</h3>
                            <p class="text-white/90 font-Jura text-sm mb-6 leading-relaxed">
                                Require specialized hydraulic equipment? Our engineering team can design and manufacture custom solutions to meet your exact specifications.
                            </p>
                            <div class="flex flex-wrap gap-2 mb-6 justify-center">
                                <span class="px-3 py-1 bg-white/20 text-white text-xs font-Jura font-medium border border-white/30 rounded-full">Custom Design</span>
                                <span class="px-3 py-1 bg-white/20 text-white text-xs font-Jura font-medium border border-white/30 rounded-full">Tailored Solutions</span>
                            </div>
                            <a href="contact.html" class="inline-flex items-center justify-center bg-white text-[#0047AB] px-6 py-3 rounded-lg font-Jura font-medium text-sm hover:bg-gray-100 transition-colors duration-200 w-full">
                                Request Quote <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Custom Build Section -->
                <div id="customBuildSection" class="mt-16">
                    <div class="text-center mb-12">
                        <span class="text-[#D22B2B] font-bold tracking-wider text-sm mb-2 block font-Jura">CUSTOM BUILD</span>
                        <h2 class="text-3xl font-Michroma font-bold text-gray-900 mb-3">Tailored Solutions</h2>
                        <div class="w-20 h-0.5 bg-[#D22B2B] rounded-full mx-auto"></div>
                        <p class="text-gray-600 font-Jura mt-6 max-w-3xl mx-auto">
                            Discover our latest custom-built hydraulic solutions, designed and manufactured to meet specific client requirements.
                        </p>
                    </div>

                    <!-- Custom Products Grid -->
                    <div id="customProductsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- Products will be loaded here dynamically -->
                        <div id="customProductsLoading" class="col-span-full text-center py-12">
                            <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500 font-Jura">Loading custom products...</p>
                        </div>

                        <div id="noCustomProducts" class="col-span-full text-center py-12 hidden">
                            <i class="fas fa-tools text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-500 font-Jura mb-2">No custom products available yet</p>
                            <p class="text-sm text-gray-400 font-Jura">Check back soon for our latest custom solutions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Color Strip -->
    <div class="bg-white">
        <div class="h-[29px] flex relative overflow-hidden">
            <!-- Blue Section (75%) with angled right edge -->
            <div class="w-3/4 bg-gradient-to-r from-blue-900 to-blue-700 relative" style="clip-path: polygon(0 0, 100% 0, calc(100% - 12px) 100%, 0 100%);">
            </div>
            <!-- White Gap -->
            <div class="w-0.5 bg-white"></div>
            <!-- Red Section with angled left edge -->
            <div class="flex-1 bg-gradient-to-r from-[#D22B2B] to-[#B02020] relative" style="clip-path: polygon(12px 0, 100% 0, 100% 100%, 0 100%);">
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-20 pb-10">
        <div class="container mx-auto px-4 max-w-7xl font-Jura">
            <div class="grid md:grid-cols-3 gap-12 mb-16">
                <!-- Company Info -->
                <div class="space-y-6">
                    <h3 class="text-xl font-Michroma font-bold">Creative Hydraulics</h3>
                    <p class="text-gray-400 leading-relaxed">
                        Innovative hydraulic solutions for industry leaders worldwide. Quality and reliability since 2008.
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-phone text-[#D22B2B] text-sm"></i>
                            <a href="tel:+919449024839" class="text-gray-400 hover:text-white transition-colors">
                                +91 ************
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-phone text-[#D22B2B] text-sm"></i>
                            <a href="tel:+918971587348" class="text-gray-400 hover:text-white transition-colors">
                                +91 ************
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-envelope text-[#D22B2B] text-sm"></i>
                            <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white transition-colors">
                                <EMAIL>
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-map-marker-alt text-[#D22B2B] text-sm"></i>
                            <span class="text-gray-400">
                                Sy. No. 325/2/C, Plot No.1, Khadarwadi Cross, Devendra Nagar, Udyambag, BELAGAVI-590008. (Karnataka)
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-display font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-4">
                        <li><a href="../index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="../index.html#certifications" class="text-gray-400 hover:text-white transition-colors">Certifications</a></li>
                        <li><a href="infrastructure.html" class="text-gray-400 hover:text-white transition-colors">Infrastructure</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Products -->
                <div>
                    <h4 class="text-lg font-display font-semibold mb-6">Our Products</h4>
                    <ul class="space-y-4">
                        <li><a href="products.html" class="text-gray-400 hover:text-white transition-colors">View All Products</a></li>
                        <li><a href="manual.html" class="text-gray-400 hover:text-white transition-colors">Manual Systems</a></li>
                        <li><a href="semi.html" class="text-gray-400 hover:text-white transition-colors">Semi-Automatic Systems</a></li>
                        <li><a href="auto.html" class="text-gray-400 hover:text-white transition-colors">Fully Automatic Systems</a></li>
                        <li><a href="hts.html" class="text-gray-400 hover:text-white transition-colors">Horizontal Test Stand</a></li>
                        <li><a href="vts.html" class="text-gray-400 hover:text-white transition-colors">Vertical Test Stand</a></li>
                        <li><a href="mshts.html" class="text-gray-400 hover:text-white transition-colors">Multi-Station Test Stand</a></li>
                        <li><a href="fsts.html" class="text-gray-400 hover:text-white transition-colors">Fire Safe Test Stand</a></li>
                        <li><a href="cts.html" class="text-gray-400 hover:text-white transition-colors">Cryogenic Test Stand</a></li>
                        <li><a href="fet.html" class="text-gray-400 hover:text-white transition-colors">Fugitive Emission Test</a></li>
                        <li><a href="hp.html" class="text-gray-400 hover:text-white transition-colors">Hydraulic Presses</a></li>
                        <li><a href="spm.html" class="text-gray-400 hover:text-white transition-colors">Special Purpose Machine</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <p class="text-gray-400 text-sm">
                        © 2024 Creative Hydraulics. All rights reserved.
                    </p>
                    <div class="flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="LinkedIn">
                            <i class="fab fa-linkedin text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Twitter">
                            <i class="fab fa-twitter text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Facebook">
                            <i class="fab fa-facebook text-lg"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile Products Dropdown Toggle
        function toggleMobileDropdown() {
            const dropdown = document.getElementById('mobileProductsDropdown');
            const icon = document.getElementById('mobileDropdownIcon');
            
            dropdown.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

        // Initialize mobile dropdown as open on products page
        document.addEventListener('DOMContentLoaded', function() {
            // Keep products dropdown open on products page
            const dropdown = document.getElementById('mobileProductsDropdown');
            const icon = document.getElementById('mobileDropdownIcon');
            if (dropdown && icon) {
                dropdown.classList.remove('hidden');
                icon.classList.add('rotate-180');
            }
        });

            

        document.addEventListener('DOMContentLoaded', function() {
            // Mobile Menu Functionality
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuContent = document.getElementById('mobile-menu-content');
            const mobileMenuBackdrop = document.getElementById('mobile-menu-backdrop');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            function toggleMobileMenu() {
                mobileMenu.classList.toggle('hidden');
                setTimeout(() => {
                    mobileMenuBackdrop.classList.toggle('opacity-0');
                    mobileMenuContent.classList.toggle('translate-x-full');
                }, 50);
            }

            if (mobileMenuButton) mobileMenuButton.addEventListener('click', toggleMobileMenu);
            if (mobileMenuClose) mobileMenuClose.addEventListener('click', toggleMobileMenu);
            if (mobileMenuBackdrop) mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

            // Close mobile menu when clicking on a link
            const mobileMenuLinks = mobileMenu?.querySelectorAll('a');
            mobileMenuLinks?.forEach(link => {
                link.addEventListener('click', toggleMobileMenu);
            });

            // Make product cards clickable
            const productCards = document.querySelectorAll('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 .bg-white.rounded-xl');
            
            productCards.forEach(card => {
                // Skip the Contact CTA card
                if (card.querySelector('.bg-gradient-to-br')) {
                    return;
                }
                
                // Add cursor pointer to indicate clickable
                card.style.cursor = 'pointer';
                
                // Add click event listener
                card.addEventListener('click', function(e) {
                    // Don't trigger if clicking on the "Learn More" link
                    if (e.target.closest('a')) {
                        return;
                    }
                    
                    // Find the link within the card
                    const link = card.querySelector('a[href]');
                    if (link && link.href) {
                        window.location.href = link.href;
                    }
                });
                
                // Add hover effect for better UX
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-database-compat.js"></script>

    <!-- Firebase Configuration and Custom Products Loading -->
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC8aAJOv3r43YscYB9A9jZFadRn_1GRG7E",
            authDomain: "chwk-edca8.firebaseapp.com",
            projectId: "chwk-edca8",
            storageBucket: "chwk-edca8.firebasestorage.app",
            messagingSenderId: "456605094673",
            appId: "1:456605094673:web:609879171843944d1db2b0",
            databaseURL: "https://chwk-edca8-default-rtdb.firebaseio.com/"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const database = firebase.database();

        console.log('Firebase initialized for products page');

        // Load Custom Products
        function loadCustomProducts() {
            const customProductsGrid = document.getElementById('customProductsGrid');
            const loadingElement = document.getElementById('customProductsLoading');
            const noProductsElement = document.getElementById('noCustomProducts');

            if (!customProductsGrid) {
                console.log('Custom products grid not found');
                return;
            }

            console.log('Loading custom products...');

            database.ref('customProducts').on('value', (snapshot) => {
                const products = snapshot.val();
                console.log('Custom products loaded:', products);

                // Hide loading
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }

                if (!products || Object.keys(products).length === 0) {
                    // Show no products message
                    if (noProductsElement) {
                        noProductsElement.classList.remove('hidden');
                    }
                    return;
                }

                // Hide no products message
                if (noProductsElement) {
                    noProductsElement.classList.add('hidden');
                }

                // Convert to array and sort by timestamp (newest first)
                const productsArray = Object.entries(products).sort((a, b) => {
                    const aTime = a[1].timestamp || new Date(a[1].createdAt).getTime() || 0;
                    const bTime = b[1].timestamp || new Date(b[1].createdAt).getTime() || 0;
                    return bTime - aTime;
                });

                // Generate HTML for products
                const productsHtml = productsArray.map(([id, product]) => `
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-200">
                        <div class="relative h-64 overflow-hidden">
                            <img src="${product.imageUrl}"
                                 alt="${product.name}"
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                 onerror="this.src='https://via.placeholder.com/400x300/f3f4f6/9ca3af?text=Image+Not+Found'">
                            <div class="absolute top-4 right-4">
                                <span class="px-3 py-1 bg-[#D22B2B] text-white text-sm font-Jura font-medium rounded-full">Custom</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900 mb-2">${product.name}</h3>
                            <p class="text-gray-600 font-Jura text-xs mb-3 leading-relaxed">
                                ${product.description}
                            </p>
                            <div class="flex flex-wrap gap-1 mb-3">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Custom Built</span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Tailored Solution</span>
                            </div>
                            <a href="contact.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-xs">
                                Get Quote <i class="fas fa-arrow-right ml-2 text-xs"></i>
                            </a>
                        </div>
                    </div>
                `).join('');

                // Update the grid content
                customProductsGrid.innerHTML = productsHtml;
            }, (error) => {
                console.error('Error loading custom products:', error);

                // Hide loading and show error message
                if (loadingElement) {
                    loadingElement.innerHTML = `
                        <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-4"></i>
                        <p class="text-red-500 font-Jura">Error loading custom products</p>
                    `;
                }
            });
        }

        // Load custom products when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for Firebase to initialize
            setTimeout(loadCustomProducts, 1000);
        });
    </script>
</body>

</html>
