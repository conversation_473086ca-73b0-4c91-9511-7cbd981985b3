@echo off
echo ========================================
echo Creative Hydraulics Admin Server Setup
echo ========================================
echo.

echo Installing Node.js dependencies...
npm install

echo.
echo Creating environment file...
if not exist .env (
    copy .env.example .env
    echo Environment file created. Please edit .env with your Firebase credentials.
) else (
    echo Environment file already exists.
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit .env file with your Firebase credentials
echo 2. Run: npm start
echo 3. Open: http://localhost:3000/admin
echo.
echo For development with auto-restart:
echo Run: npm run dev
echo.
pause
