<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Admin - Creative Hydraulics</title>
    <link rel="icon" type="image/png" href="../assets/logos/logo.png">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Jura:wght@300..700&family=Michroma&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'Jura': ['Jura', 'sans-serif'],
                        'Michroma': ['Michroma', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    
    <style>
        .font-Michroma { font-family: 'Michroma', sans-serif; }
        .font-Jura { font-family: 'Jura', sans-serif; }
        
        .upload-area {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #0047AB;
            background-color: #f8fafc;
        }
        
        .upload-area.dragover {
            border-color: #0047AB;
            background-color: #eff6ff;
        }
    </style>
</head>

<body class="font-body bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="../assets/logos/logo.png" alt="Creative Hydraulics Logo" class="w-12 h-12 object-contain" />
                    <div>
                        <h1 class="text-xl font-Michroma font-bold text-gray-900">Creative Hydraulics</h1>
                        <p class="text-sm font-Jura text-gray-600">Simple Product Admin</p>
                    </div>
                </div>
                <a href="../pages/products.html" target="_blank" class="bg-[#0047AB] hover:bg-[#003A8C] text-white px-6 py-2 rounded-lg font-Jura font-medium transition-colors">
                    <i class="fas fa-external-link-alt mr-2"></i>View Products
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <div class="max-w-2xl mx-auto">
            <!-- Status -->
            <div id="firebaseStatus" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-database text-blue-600 mr-2"></i>
                    <span class="font-Jura font-medium text-blue-800">Firebase Status: </span>
                    <span id="statusText" class="ml-2 font-Jura text-blue-600">Initializing...</span>
                </div>
            </div>

            <!-- Success Message -->
            <div id="successMessage" class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Product added successfully!</span>
                </div>
            </div>

            <!-- Add Product Form -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h2 class="text-2xl font-Michroma font-bold text-gray-900 mb-6">Add Custom Product</h2>
                
                <form id="productForm" class="space-y-6">
                    <!-- Product Name -->
                    <div>
                        <label for="productName" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="productName" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura"
                               placeholder="Enter product name">
                    </div>

                    <!-- Product Description -->
                    <div>
                        <label for="productDescription" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Description <span class="text-red-500">*</span>
                        </label>
                        <textarea id="productDescription" required rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura resize-none"
                                  placeholder="Enter detailed product description"></textarea>
                    </div>

                    <!-- Product Image -->
                    <div>
                        <label class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Image <span class="text-red-500">*</span>
                        </label>
                        <div id="uploadArea" class="upload-area p-8 rounded-lg text-center cursor-pointer">
                            <div id="uploadContent">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600 font-Jura mb-2">Click to upload or drag and drop</p>
                                <p class="text-sm text-gray-500 font-Jura">PNG, JPG, JPEG up to 10MB</p>
                            </div>
                            <div id="imagePreview" class="hidden">
                                <img id="previewImg" class="max-w-48 max-h-48 object-cover rounded-lg mx-auto mb-4" alt="Preview">
                                <p class="text-sm text-gray-600 font-Jura">Click to change image</p>
                            </div>
                        </div>
                        <input type="file" id="imageInput" accept="image/*" class="hidden">
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4">
                        <button type="button" id="resetBtn" 
                                class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-Jura font-medium transition-colors">
                            Reset Form
                        </button>
                        <button type="submit" id="submitBtn"
                                class="px-6 py-3 bg-[#0047AB] hover:bg-[#003A8C] text-white rounded-lg font-Jura font-medium transition-colors">
                            <span id="submitText">Add Product</span>
                            <span id="loadingSpinner" class="hidden">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Adding...
                            </span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Recent Products -->
            <div class="bg-white rounded-xl shadow-lg p-8 mt-8">
                <h3 class="text-xl font-Michroma font-bold text-gray-900 mb-6">Recent Products</h3>
                <div id="recentProducts" class="space-y-4">
                    <p class="text-gray-500 font-Jura text-center py-8">Loading products...</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC8aAJOv3r43YscYB9A9jZFadRn_1GRG7E",
            authDomain: "chwk-edca8.firebaseapp.com",
            projectId: "chwk-edca8",
            storageBucket: "chwk-edca8.firebasestorage.app",
            messagingSenderId: "456605094673",
            appId: "1:456605094673:web:609879171843944d1db2b0",
            databaseURL: "https://chwk-edca8-default-rtdb.firebaseio.com/"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const database = firebase.database();
        const storage = firebase.storage();

        console.log('Firebase initialized with compat SDK');

        // DOM Elements
        const productForm = document.getElementById('productForm');
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        const uploadContent = document.getElementById('uploadContent');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const successMessage = document.getElementById('successMessage');
        const resetBtn = document.getElementById('resetBtn');
        const recentProducts = document.getElementById('recentProducts');
        const statusText = document.getElementById('statusText');

        let selectedFile = null;

        // Update status
        statusText.textContent = 'Connected';
        statusText.className = 'ml-2 font-Jura text-green-600';

        // Image upload handling
        uploadArea.addEventListener('click', () => imageInput.click());

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) handleFileSelect(files[0]);
        });

        imageInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) handleFileSelect(e.target.files[0]);
        });

        function handleFileSelect(file) {
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file');
                return;
            }
            if (file.size > 10 * 1024 * 1024) {
                alert('File size must be less than 10MB');
                return;
            }

            selectedFile = file;
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImg.src = e.target.result;
                uploadContent.classList.add('hidden');
                imagePreview.classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }

        // Form submission
        productForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const productName = document.getElementById('productName').value.trim();
            const productDescription = document.getElementById('productDescription').value.trim();

            if (!productName || !productDescription || !selectedFile) {
                alert('Please fill in all fields and select an image');
                return;
            }

            // Show loading
            submitBtn.disabled = true;
            submitText.classList.add('hidden');
            loadingSpinner.classList.remove('hidden');

            try {
                console.log('Starting upload...');
                
                // Upload image
                const timestamp = Date.now();
                const fileName = `${timestamp}_${selectedFile.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
                const storageRef = storage.ref(`products/${fileName}`);
                
                const uploadTask = await storageRef.put(selectedFile);
                const imageUrl = await uploadTask.ref.getDownloadURL();
                
                console.log('Image uploaded:', imageUrl);

                // Save to database
                const productData = {
                    name: productName,
                    description: productDescription,
                    imageUrl: imageUrl,
                    timestamp: firebase.database.ServerValue.TIMESTAMP,
                    createdAt: new Date().toISOString()
                };

                await database.ref('customProducts').push(productData);
                console.log('Product saved to database');

                // Show success
                successMessage.classList.remove('hidden');
                setTimeout(() => successMessage.classList.add('hidden'), 5000);

                // Reset form
                resetForm();
                loadRecentProducts();

            } catch (error) {
                console.error('Error:', error);
                alert('Error adding product: ' + error.message);
            } finally {
                submitBtn.disabled = false;
                submitText.classList.remove('hidden');
                loadingSpinner.classList.add('hidden');
            }
        });

        // Reset form
        resetBtn.addEventListener('click', resetForm);

        function resetForm() {
            productForm.reset();
            selectedFile = null;
            uploadContent.classList.remove('hidden');
            imagePreview.classList.add('hidden');
        }

        // Load recent products
        function loadRecentProducts() {
            database.ref('customProducts').orderByChild('timestamp').limitToLast(5).on('value', (snapshot) => {
                const products = snapshot.val();
                if (!products) {
                    recentProducts.innerHTML = '<p class="text-gray-500 font-Jura text-center py-8">No products yet</p>';
                    return;
                }

                const productsArray = Object.entries(products).reverse();
                const html = productsArray.map(([id, product]) => `
                    <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                        <img src="${product.imageUrl}" alt="${product.name}" class="w-16 h-16 object-cover rounded-lg">
                        <div class="flex-1">
                            <h4 class="font-Jura font-semibold text-gray-900">${product.name}</h4>
                            <p class="text-sm text-gray-600 font-Jura">${product.description.substring(0, 100)}...</p>
                        </div>
                        <button onclick="deleteProduct('${id}')" class="text-red-500 hover:text-red-700 p-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `).join('');

                recentProducts.innerHTML = html;
            });
        }

        // Delete product
        window.deleteProduct = async (id) => {
            if (confirm('Delete this product?')) {
                try {
                    await database.ref(`customProducts/${id}`).remove();
                } catch (error) {
                    alert('Error deleting product: ' + error.message);
                }
            }
        };

        // Load products on start
        loadRecentProducts();
    </script>
</body>
</html>
