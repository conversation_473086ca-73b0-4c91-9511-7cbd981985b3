# Creative Hydraulics Website

A professional website for Creative Hydraulics - Leading manufacturer of hydraulic equipment and testing solutions.

## 🚀 Project Structure

```
tri/
├── index.html                 # Main website file
├── README.md                 # Project documentation
├── .gitignore               # Git ignore patterns
├── pages/                    # All website pages organized by type
│   ├── about.html           # Company introduction page
│   ├── contact.html         # Contact information and form
│   ├── gallery.html         # Product and project gallery
│   ├── infrastructure.html  # Company facilities page
│   ├── management.html      # Leadership and team page
│   ├── hts.html            # Horizontal Test Stand product page
│   ├── vts.html            # Vertical Test Stand product page
│   ├── mshts.html          # Multi-Station Hydro Test Stand page
│   ├── cts.html            # Cryogenic Test Stand page
│   ├── fet.html            # Fugitive Emission Test page
│   ├── fsts.html           # Fire Safety Test Stand page
│   ├── spm.html            # Special Purpose Machine page
│   └── hp.html             # Hydraulic Press page
├── assets/                   # All media assets organized by type
│   ├── images/              # Image files
│   │   ├── hcmYN.jpg       # Background image for clients section
│   │   ├── lol.jpg         # Global export background
│   │   ├── hydrulic_pump.jpg # Product image
│   │   ├── cylinder.jpg     # Product image
│   │   └── reference.jpg    # Reference image
│   ├── videos/              # Video files
│   │   ├── bg-vid.mp4      # Main background video
│   │   ├── bg-vid1.mp4     # Alternative background video
│   │   └── 7622781-uhd_2560_1440_25fps.mp4 # Hero section video
│   └── logos/               # Logo and branding assets
│       ├── logo.png         # Main company logo
│       ├── CREATIVEHYDRAULICS.png  # Company logo variant 1
│       └── CREATIVEHYDRAULICS1.png # Company logo variant 2
├── css/                     # CSS files (for future external stylesheets)
├── js/                      # JavaScript files
│   └── script.js           # Main JavaScript functionality
└── docs/                    # Documentation and additional files
    └── context.md          # Project context and requirements
```

## 🎯 Features

### Navigation Structure
- **Home** (`index.html`) - Landing page with hero video carousel
- **About Us** - Company information and leadership
  - **Introduction** (`pages/about.html`) - Company overview and profile
  - **Management** (`pages/management.html`) - Leadership and team structure
- **Certifications** - Quality certifications and standards
- **Products** - Complete product catalog with dedicated pages:
  - **Horizontal Test Stand** (`pages/hts.html`) - HTS pressure testing solutions
  - **Vertical Test Stand** (`pages/vts.html`) - VTS vertical testing equipment
  - **Multi-Station Hydro Test Stand** (`pages/mshts.html`) - MSHTS multi-station solutions
  - **Fire Safety Test Stand** (`pages/fsts.html`) - Fire safety testing equipment
  - **Cryogenic Test Stand** (`pages/cts.html`) - Low temperature testing
  - **Fugitive Emission Test** (`pages/fet.html`) - FET emission testing solutions
  - **Hydraulic Presses** (`pages/hp.html`) - Industrial pressing solutions
  - **Special Purpose Machine** (`pages/spm.html`) - SPM custom solutions
- **Infrastructure** (`pages/infrastructure.html`) - Company facilities and capabilities
- **Gallery** (`pages/gallery.html`) - Image gallery of products and projects
- **Contact Us** (`pages/contact.html`) - Contact form and company information

### Key Sections
1. **Hero Section** - Auto-playing video carousel with company messaging
2. **About Section** - Company overview with leadership information
3. **Products Section** - Interactive product showcase with modal views
4. **Global Export** - International presence and markets
5. **Clients Section** - Auto-scrolling client logos with industry leaders
6. **Testimonials** - Customer feedback and reviews
7. **Contact Section** - Contact form with Google Maps integration

## 🛠️ Technologies Used

- **HTML5** - Semantic markup structure
- **Tailwind CSS** - Utility-first CSS framework
- **JavaScript** - Interactive functionality
- **Swiper.js** - Touch slider/carousel library
- **Font Awesome** - Icon library
- **Google Fonts** - Typography (Inter, Outfit, Jura, Michroma)

## 📱 Responsive Design

The website is fully responsive and optimized for:
- Desktop computers (1920px+)
- Tablets (768px - 1024px)
- Mobile devices (320px - 767px)

## 🎨 Design Features

- **Professional Corporate Design** - Clean, modern layout
- **Smooth Animations** - CSS transitions and hover effects
- **Interactive Elements** - Dropdown menus, modals, carousels
- **Auto-scrolling Sections** - Dynamic client logo display
- **Video Integration** - Background videos with fallback images
- **Color Scheme** - Blue (#0047AB) and Red (#D22B2B) corporate colors

## 📂 File Organization

### Project Structure
- **Root** - Contains main landing page (`index.html`) and project files
- **Pages** (`pages/`) - All website pages organized in dedicated folder
- **Assets** (`assets/`) - All media assets organized by type
  - **Images** (`assets/images/`) - All JPG/PNG image files
  - **Videos** (`assets/videos/`) - All MP4 video files  
  - **Logos** (`assets/logos/`) - Company logos and branding assets
- **Code** - Organized development files
  - **CSS** (`css/`) - External stylesheets (currently using inline styles)
  - **JavaScript** (`js/`) - External JavaScript files
- **Documentation** (`docs/`) - Project documentation and additional files

## 🚀 Getting Started

1. **Clone or download** the project files
2. **Open** `index.html` in a web browser
3. **Ensure** all asset paths are correctly linked
4. **Test** on different devices for responsive behavior
5. **Navigate** through pages using the navigation menu

## 📧 Contact Information

- **Email**: <EMAIL>
- **Phone**: +91 ************
- **Location**: Belagavi, Karnataka, India

## 🔧 Development Notes

- All styles are currently inline within the HTML files
- External CDN links are used for frameworks (Tailwind, Swiper, Font Awesome)
- Images are optimized for web performance
- Videos include fallback poster images
- Google Maps integration for location display
- Clean folder structure for easy maintenance and navigation

## 📝 License

This project is proprietary to Creative Hydraulics. All rights reserved.

---

**Creative Hydraulics** - Innovative Hydraulic Solutions for Industry Leaders Worldwide 