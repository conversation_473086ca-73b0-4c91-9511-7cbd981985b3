# Creative Hydraulics Admin Server

A professional Node.js Express server for managing Creative Hydraulics products with Firebase integration.

## 🚀 Features

- **Node.js Express Server** - Professional backend architecture
- **Firebase Admin SDK** - Server-side Firebase integration
- **File Upload** - Multer-powered image uploads
- **Security** - Helmet, CORS, Rate limiting
- **API Endpoints** - RESTful API for product management
- **Real-time Updates** - Products sync with main website
- **Error Handling** - Comprehensive error management
- **Logging** - Morgan request logging

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Firebase project with Admin SDK credentials

## 🛠 Installation

### Windows
```bash
cd server
install.bat
```

### Linux/Mac
```bash
cd server
chmod +x install.sh
./install.sh
```

### Manual Installation
```bash
cd server
npm install
cp .env.example .env
# Edit .env with your Firebase credentials
```

## ⚙️ Configuration

1. **Edit `.env` file** with your Firebase credentials:
```env
FIREBASE_PROJECT_ID=chwk-edca8
FIREBASE_PRIVATE_KEY="-----B<PERSON><PERSON> PRIVATE KEY-----\nYour_Key_Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_DATABASE_URL=https://chwk-edca8-default-rtdb.firebaseio.com/
FIREBASE_STORAGE_BUCKET=chwk-edca8.firebasestorage.app
```

2. **Get Firebase Admin Credentials**:
   - Go to Firebase Console → Project Settings → Service Accounts
   - Generate new private key
   - Copy credentials to `.env` file

## 🚀 Running the Server

### Development Mode (with auto-restart)
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

## 📱 Access Points

- **Admin Panel**: http://localhost:3000/admin
- **Server Admin**: http://localhost:3000/admin (server-powered)
- **API Health**: http://localhost:3000/api/health
- **Products Page**: http://localhost:3000/pages/products.html

## 🔌 API Endpoints

### GET /api/health
Check server status and Firebase connection

### GET /api/products
Get all products
```json
{
  "success": true,
  "products": [...],
  "count": 5
}
```

### POST /api/products
Add new product (multipart/form-data)
- `name` (string, required)
- `description` (string, required)
- `image` (file, required)

### DELETE /api/products/:id
Delete product by ID

## 🔒 Security Features

- **Helmet** - Security headers
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - Prevent abuse
- **File Validation** - Image type and size limits
- **Input Validation** - Request validation
- **Error Handling** - Secure error responses

## 📁 Project Structure

```
server/
├── server.js              # Main server file
├── package.json           # Dependencies
├── .env.example          # Environment template
├── .env                  # Environment variables (create this)
├── public/
│   └── admin.html        # Server-based admin panel
├── install.bat           # Windows installer
├── install.sh            # Linux/Mac installer
└── README.md             # This file
```

## 🔧 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment | development |
| `FIREBASE_PROJECT_ID` | Firebase project ID | chwk-edca8 |
| `FIREBASE_DATABASE_URL` | Firebase database URL | Required |
| `FIREBASE_STORAGE_BUCKET` | Firebase storage bucket | Required |
| `MAX_FILE_SIZE` | Max upload size (bytes) | 10485760 (10MB) |
| `RATE_LIMIT_MAX_REQUESTS` | Rate limit max requests | 100 |

## 🐛 Troubleshooting

### Firebase Connection Issues
1. Check `.env` file has correct credentials
2. Verify Firebase project settings
3. Check console logs for detailed errors

### File Upload Issues
1. Check file size (max 10MB)
2. Verify file type (images only)
3. Check Firebase Storage permissions

### Server Won't Start
1. Check Node.js version (v16+)
2. Run `npm install` again
3. Check port 3000 is available

## 📊 Monitoring

- **Health Check**: `/api/health`
- **Server Logs**: Console output with Morgan
- **Error Tracking**: Comprehensive error logging

## 🔄 Workflow

1. **Admin uploads product** → Server receives request
2. **Image uploads to Firebase Storage** → Gets public URL
3. **Product data saves to Firebase Database** → With image URL
4. **Products page auto-updates** → Real-time display

## 🚀 Deployment

### Local Development
```bash
npm run dev
```

### Production Deployment
1. Set `NODE_ENV=production` in `.env`
2. Configure production Firebase credentials
3. Use process manager (PM2, Forever)
4. Set up reverse proxy (Nginx)

### PM2 Deployment
```bash
npm install -g pm2
pm2 start server.js --name "creative-hydraulics-admin"
pm2 startup
pm2 save
```

## 📞 Support

For technical support or questions:
- Check server logs for errors
- Verify Firebase configuration
- Test API endpoints with `/api/health`

---

© 2024 Creative Hydraulics. All rights reserved.
