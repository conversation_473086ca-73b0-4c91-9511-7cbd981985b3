// Admin Panel JavaScript - No Authentication Required
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin panel loaded successfully!');

    // Wait for Firebase to be available
    const checkFirebase = () => {
        if (typeof window.firebaseDatabase === 'undefined' || typeof window.firebaseStorage === 'undefined') {
            console.log('Waiting for Firebase to initialize...');
            setTimeout(checkFirebase, 500);
            return;
        }
        console.log('Firebase is ready!');
        initializeAdmin();
    };

    setTimeout(checkFirebase, 500);
});

function initializeAdmin() {
    console.log('Initializing admin panel...');

    // DOM Elements
    const productForm = document.getElementById('productForm');
    const uploadArea = document.getElementById('uploadArea');
    const imageInput = document.getElementById('imageInput');
    const uploadContent = document.getElementById('uploadContent');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const successMessage = document.getElementById('successMessage');
    const resetBtn = document.getElementById('resetBtn');
    const recentProducts = document.getElementById('recentProducts');
    const firebaseStatus = document.getElementById('firebaseStatus');
    const testFirebaseBtn = document.getElementById('testFirebase');

    let selectedFile = null;

    // Update Firebase status
    function updateFirebaseStatus() {
        if (window.firebaseDatabase && window.firebaseStorage) {
            firebaseStatus.textContent = 'Connected';
            firebaseStatus.className = 'ml-2 font-Jura text-green-600';
        } else {
            firebaseStatus.textContent = 'Not Connected';
            firebaseStatus.className = 'ml-2 font-Jura text-red-600';
        }
    }

    // Test Firebase connection
    testFirebaseBtn.addEventListener('click', async () => {
        try {
            console.log('Testing Firebase connection...');
            testFirebaseBtn.disabled = true;
            testFirebaseBtn.textContent = 'Testing...';

            // Test database write using compat SDK
            await window.firebaseDatabase.ref('test').set({
                timestamp: window.firebase.database.ServerValue.TIMESTAMP,
                message: 'Connection test successful'
            });

            alert('Firebase connection test successful!');
            updateFirebaseStatus();
        } catch (error) {
            console.error('Firebase test failed:', error);
            alert('Firebase test failed: ' + error.message);
        } finally {
            testFirebaseBtn.disabled = false;
            testFirebaseBtn.textContent = 'Test Connection';
        }
    });

    updateFirebaseStatus();

    // Product name change handler to update storage info
    const productNameInput = document.getElementById('productName');
    if (productNameInput) {
        productNameInput.addEventListener('input', () => {
            if (selectedFile) {
                updateStorageInfo(selectedFile);
            }
        });
    }

    // Image Upload Handling
    uploadArea.addEventListener('click', () => {
        imageInput.click();
    });

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    imageInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    function handleFileSelect(file) {
        console.log('File selected:', file.name);

        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file (PNG, JPG, JPEG)');
            return;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size must be less than 10MB');
            return;
        }

        selectedFile = file;

        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            uploadContent.classList.add('hidden');
            imagePreview.classList.remove('hidden');

            // Show file info
            const fileInfo = document.getElementById('fileInfo');
            if (fileInfo) {
                fileInfo.innerHTML = `
                    <strong>File:</strong> ${file.name}<br>
                    <strong>Size:</strong> ${formatFileSize(file.size)}<br>
                    <strong>Type:</strong> ${file.type}
                `;
            }

            // Update storage info
            updateStorageInfo(file);
        };
        reader.readAsDataURL(file);
    }

    // Update storage information display
    function updateStorageInfo(file) {
        const storageInfo = document.getElementById('storageInfo');
        const storageFileName = document.getElementById('storageFileName');

        if (storageInfo && storageFileName) {
            const productName = document.getElementById('productName').value.trim() || 'product';
            const timestamp = Date.now();
            const sanitizedProductName = productName.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30);
            const fileExtension = file.name.split('.').pop();
            const fileName = `${timestamp}_${sanitizedProductName}.${fileExtension}`;

            storageFileName.textContent = fileName;
            storageInfo.classList.remove('hidden');
        }
    }

    // Form Submission
    productForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        console.log('Form submitted');
        
        const productName = document.getElementById('productName').value.trim();
        const productDescription = document.getElementById('productDescription').value.trim();

        // Validation
        if (!productName || !productDescription || !selectedFile) {
            alert('Please fill in all required fields and select an image');
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        loadingSpinner.classList.add('show');

        try {
            console.log('Starting upload process...');
            console.log('Firebase Storage available:', !!window.firebaseStorage);
            console.log('Firebase Database available:', !!window.firebaseDatabase);

            if (!window.firebaseStorage || !window.firebaseDatabase) {
                throw new Error('Firebase not properly initialized');
            }

            // Upload image to Firebase Storage using compat SDK
            const timestamp = Date.now();
            // Create a more descriptive filename with product name
            const sanitizedProductName = productName.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30);
            const fileExtension = selectedFile.name.split('.').pop();
            const fileName = `${timestamp}_${sanitizedProductName}.${fileExtension}`;
            const storageRef = window.firebaseStorage.ref(`products/${fileName}`);

            console.log('Uploading image to Firebase Storage...', fileName);
            const uploadTask = await storageRef.put(selectedFile);
            console.log('Image uploaded successfully', uploadTask);

            const imageUrl = await uploadTask.ref.getDownloadURL();
            console.log('Image URL obtained:', imageUrl);

            // Save product data to Firebase Realtime Database using compat SDK
            const productData = {
                name: productName,
                description: productDescription,
                imageUrl: imageUrl,
                fileName: fileName, // Store the filename for reference
                originalFileName: selectedFile.name, // Store original filename
                fileSize: selectedFile.size, // Store file size
                fileType: selectedFile.type, // Store file type
                timestamp: window.firebase.database.ServerValue.TIMESTAMP,
                createdAt: new Date().toISOString()
            };

            console.log('Saving product data to database...', productData);
            await window.firebaseDatabase.ref('customProducts').push(productData);
            console.log('Product saved successfully');

            // Show success message
            successMessage.classList.add('show');
            setTimeout(() => {
                successMessage.classList.remove('show');
            }, 5000);

            // Reset form
            resetForm();

            // Refresh recent products
            loadRecentProducts();

        } catch (error) {
            console.error('Detailed error:', error);
            console.error('Error stack:', error.stack);
            alert('Error adding product: ' + error.message + '\nCheck console for details.');
        } finally {
            // Hide loading state
            submitBtn.disabled = false;
            submitText.classList.remove('hidden');
            loadingSpinner.classList.remove('show');
        }
    });

    // Reset Form
    resetBtn.addEventListener('click', resetForm);

    function resetForm() {
        console.log('Resetting form');
        productForm.reset();
        selectedFile = null;
        uploadContent.classList.remove('hidden');
        imagePreview.classList.add('hidden');
        previewImg.src = '';

        // Hide storage info
        const storageInfo = document.getElementById('storageInfo');
        if (storageInfo) {
            storageInfo.classList.add('hidden');
        }
    }

    // Load Recent Products
    function loadRecentProducts() {
        console.log('Loading recent products...');

        window.firebaseDatabase.ref('customProducts').orderByChild('timestamp').limitToLast(5).on('value', (snapshot) => {
            const products = snapshot.val();
            console.log('Products loaded:', products);

            if (!products) {
                recentProducts.innerHTML = '<p class="text-gray-500 font-Jura text-center py-8">No products added yet</p>';
                return;
            }

            // Convert to array and reverse to show newest first
            const productsArray = Object.entries(products).reverse();

            // Display recent products
            const recentProductsHtml = productsArray.map(([id, product]) => `
                <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <img src="${product.imageUrl}" alt="${product.name}" class="w-16 h-16 object-cover rounded-lg shadow-sm">
                    <div class="flex-1">
                        <h4 class="font-Jura font-semibold text-gray-900">${product.name}</h4>
                        <p class="text-sm text-gray-600 font-Jura line-clamp-2">${product.description.substring(0, 100)}...</p>
                        <div class="flex items-center space-x-4 mt-1">
                            <p class="text-xs text-gray-500 font-Jura">
                                Added: ${formatDate(product.createdAt)}
                            </p>
                            ${product.fileSize ? `<p class="text-xs text-gray-500 font-Jura">
                                Size: ${formatFileSize(product.fileSize)}
                            </p>` : ''}
                            ${product.originalFileName ? `<p class="text-xs text-gray-500 font-Jura" title="${product.originalFileName}">
                                File: ${truncateText(product.originalFileName, 20)}
                            </p>` : ''}
                        </div>
                    </div>
                    <button onclick="deleteProduct('${id}')" class="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');

            recentProducts.innerHTML = recentProductsHtml;
        }, (error) => {
            console.error('Error loading products:', error);
            recentProducts.innerHTML = '<p class="text-red-500 font-Jura text-center py-8">Error loading products</p>';
        });
    }

    // Delete Product Function
    window.deleteProduct = async function(productId) {
        if (!confirm('Are you sure you want to delete this product?')) {
            return;
        }

        try {
            console.log('Deleting product:', productId);
            await window.firebaseDatabase.ref(`customProducts/${productId}`).remove();

            // Show success message
            const tempMessage = document.createElement('div');
            tempMessage.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6';
            tempMessage.innerHTML = '<div class="flex items-center"><i class="fas fa-check-circle mr-2"></i><span>Product deleted successfully!</span></div>';

            document.querySelector('main .container').insertBefore(tempMessage, document.querySelector('.bg-white'));

            setTimeout(() => {
                tempMessage.remove();
            }, 3000);

        } catch (error) {
            console.error('Error deleting product:', error);
            alert('Error deleting product: ' + error.message);
        }
    };

    // Format date helper
    function formatDate(dateString) {
        if (!dateString) return 'Recently';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch (e) {
            return 'Recently';
        }
    }

    // Load recent products on page load
    setTimeout(() => {
        loadRecentProducts();
    }, 1000); // Wait for Firebase to initialize
}

// Utility function to truncate text
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substr(0, maxLength) + '...';
}

// Utility function to format file size
function formatFileSize(bytes) {
    if (!bytes) return 'Unknown';
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
