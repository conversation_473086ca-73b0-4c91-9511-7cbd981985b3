<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creative Hydraulics - Innovative Hydraulic Solutions</title>
    <link rel="icon" type="image/png" href="assets/logos/logo.png">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Outfit:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Jura:wght@300..700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Michroma&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'display': ['Outfit', 'sans-serif'],
                        'Jura': ['Jura', 'sans-serif'],
                        'Michroma': ['Michroma', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        .hero-gradient {
            background: linear-gradient(rgba(0, 48, 100, 0.7), rgba(0, 48, 100, 0.9));
        }

        .nav-link {
            position: relative;
        }

        .nav-link {
            color: #374151; /* Default text color */
        }

        .nav-link-active {
            color: #D22B2B !important; /* Red text for active page */
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background-color: #2563eb; /* Blue underline */
            transition: all 0.3s ease-in-out;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .top-bar-link {
            transition: all 0.3s ease;
        }

        .top-bar-link:hover {
            color: #60a5fa;
        }

        .hero-slide {
            transform: scale(1.1);
            transition: transform 8s ease;
        }

        .hero-slide.active {
            transform: scale(1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        /* Enhanced Top Bar and Navigation Styles */
        .top-bar-separator {
            width: 1px;
            height: 16px;
            background: rgba(255, 255, 255, 0.2);
            margin: 0 1.5rem;
        }

        .social-icon {
            @apply w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300;
        }

        .social-icon:hover {
            @apply bg-white/10 transform -translate-y-0.5;
        }

        .nav-dropdown {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            pointer-events: none;
            top: 100%;
            right: -200px;
            z-index: 1000;
        }

        .nav-dropdown.show,
        .dropdown-parent:hover .nav-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            pointer-events: auto;
        }

        .dropdown-item {
            transition: all 0.2s ease;
            position: relative;
        }

        .dropdown-item:hover {
            background-color: #f1f5f9;
            color: #0047AB;
            padding-left: 20px;
        }

        .dropdown-item:hover::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: #0047AB;
        }

        /* Create a bridge area to prevent dropdown from disappearing */
        .dropdown-parent::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            height: 8px;
            background: transparent;
            z-index: 49;
        }

        /* Ensure dropdown stays visible when hovering over it or the bridge */
        .dropdown-parent:hover .nav-dropdown,
        .nav-dropdown:hover {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            pointer-events: auto;
        }

        /* Improved dropdown positioning and styling */
        .dropdown-parent {
            position: relative;
        }

        .nav-dropdown {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .mobile-menu-container {
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.98));
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }

            .mobile-menu-item {
                position: relative;
                transition: all 0.3s ease;
            }

            .mobile-menu-item::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 1px;
                background: linear-gradient(to right, transparent, rgba(30, 64, 175, 0.1), transparent);
            }

            .mobile-icon {
                transition: all 0.3s ease;
            }

            .mobile-icon:hover {
                transform: translateY(-1px);
            }
        }

        html {
            scroll-behavior: smooth;
        }

        .scroll-mt {
            scroll-margin-top: 6rem;
        }

        /* Product Carousel Styles */
        .product-carousel {
            scroll-behavior: smooth;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .product-carousel::-webkit-scrollbar {
            display: none;
        }

        .product-card {
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image-wrapper {
            position: relative;
            overflow: hidden;
            padding-top: 75%;
        }

        .product-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.6s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.1);
        }

        /* Carousel Navigation */
        .carousel-button {
            opacity: 0;
            transition: all 0.3s ease;
        }

        .carousel-container:hover .carousel-button {
            opacity: 1;
        }

        .carousel-button:hover {
            background-color: #1e40afe6;
        }

        .font-Michroma { font-family: 'Michroma', sans-serif; }
        .font-Jura { font-family: 'Jura', sans-serif; }
        
        .geometric-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            position: relative;
        }
        
        .geometric-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(210, 43, 43, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 71, 171, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(148, 163, 184, 0.02) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .geometric-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%),
                linear-gradient(0deg, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
            background-size: 100px 100px, 80px 80px;
            background-position: 0 0, 50px 50px;
            pointer-events: none;
        }
        
        .pattern-overlay {
            background-image: 
                radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 75px 75px, rgba(210, 43, 43, 0.03) 2px, transparent 2px);
            background-size: 100px 100px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        
        .swiper-pagination-bullet {
            background: #D22B2B;
            opacity: 0.3;
        }
        
        .swiper-pagination-bullet-active {
            opacity: 1;
            background: #D22B2B;
        }
        
        .product-card {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 480px;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-color: #d1d5db;
        }
        
        .product-card-content {
            position: relative;
            z-index: 2;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }











        .product-image-container {
            cursor: pointer;
        }

        /* Client Logo Scrolling Animations */
        @keyframes scroll-left {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        @keyframes scroll-right {
            0% {
                transform: translateX(-50%);
            }
            100% {
                transform: translateX(0);
            }
        }

        .animate-scroll-left {
            animation: scroll-left 40s linear infinite;
        }

        .animate-scroll-right {
            animation: scroll-right 60s linear infinite;
        }

        /* Mobile Optimized Scrolling - Much Faster */
        @media (max-width: 768px) {
            .animate-scroll-left {
                animation: scroll-left 8s linear infinite;
            }

            .animate-scroll-right {
                animation: scroll-right 12s linear infinite;
            }
        }

        /* Touch/Drag States */
        .client-scroll-paused {
            animation-play-state: paused !important;
        }

        .client-scroll-dragging {
            animation-play-state: paused !important;
            cursor: grabbing !important;
        }

        .client-scroll-container {
            cursor: grab;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        .client-scroll-left:hover,
        .client-scroll-right:hover {
            animation-play-state: paused;
        }



    </style>
    <!-- Add Swiper CSS in head -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
</head>

<body class="font-body">
    <!-- Top Info Bar -->
    <!-- <div class="bg-[#111827]">
        <div class="container mx-auto font-Jura">
            <div class="hidden md:flex justify-between items-center py-1 px-6">
                <div class="flex items-center space-x-6">
                    <a href="tel:09449024839"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide">
                        <i class="fas fa-phone mr-2.5 text-[#D22B2B] group-hover:rotate-12 transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5">+91 ************</span>
                    </a>
                    <a href="tel:+91 ************"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide">
                        <i class="fas fa-phone mr-2.5 text-[#D22B2B] group-hover:rotate-12 transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5">+91 ************</span>
                    </a>
                    <div class="top-bar-separator"></div>
                    <a href="mailto:<EMAIL>"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide">
                        <i class="fas fa-envelope mr-2.5 text-[#D22B2B] group-hover:scale-110 transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5"><EMAIL></span>
                    </a>
                </div>

                <div class="flex items-center">
                    <a href="#location"
                        class="flex items-center text-white/90 hover:text-white group tracking-wide mr-8">
                        <i
                            class="fas fa-map-marker-alt mr-2 text-[#D22B2B] group-hover:bounce transition-transform"></i>
                        <span class="text-sm font-medium -translate-y-0.5">Our Location</span>
                    </a>
                    <div class="top-bar-separator"></div>
                    <div class="flex items-center space-x-3 ml-8">
                        <a href="#" class="social-icon text-white/90 hover:text-white" aria-label="LinkedIn">
                            <i class="fab fa-linkedin text-[15px]"></i>
                        </a>
                        <a href="#" class="social-icon text-white/90 hover:text-white" aria-label="Twitter">
                            <i class="fab fa-twitter text-[15px]"></i>
                        </a>
                        <a href="#" class="social-icon text-white/90 hover:text-white" aria-label="Facebook">
                            <i class="fab fa-facebook text-[15px]"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="flex md:hidden justify-between items-center py-2.5 px-4">
                <a href="mailto:<EMAIL>"
                    class="flex items-center text-white/90 hover:text-white group tracking-wide">
                    <i class="fas fa-envelope mr-1.5 text-[#D22B2B] group-hover:scale-110 transition-transform"></i>
                    <span class="text-sm font-medium -translate-y-0.5"><EMAIL></span>
                </a>
                <div class="flex items-center space-x-4">
                    <a href="#"
                        class="w-7 h-7 flex items-center justify-center text-white/90 hover:text-white mobile-icon"
                        aria-label="LinkedIn">
                        <i class="fab fa-linkedin text-[15px]"></i>
                    </a>
                    <a href="#"
                        class="w-7 h-7 flex items-center justify-center text-white/90 hover:text-white mobile-icon"
                        aria-label="Twitter">
                        <i class="fab fa-twitter text-[15px]"></i>
                    </a>
                    <a href="#"
                        class="w-7 h-7 flex items-center justify-center text-white/90 hover:text-white mobile-icon"
                        aria-label="Facebook">
                        <i class="fab fa-facebook text-[15px]"></i>
                    </a>
                </div>
            </div>
        </div>
    </div> -->

    <!-- Main Navigation -->
    <nav class="bg-[#ffffff] shadow-lg sticky top-0 z-50 transition-shadow duration-300">
        <div class="container mx-auto">
            <div class="flex justify-between items-center h-20 px-4">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="/" class="flex items-center space-x-2">
                        <div
                            class="w-16 h-16 md:w-20 md:h-20 flex items-center justify-center shadow-lg">
                            <img src="assets/logos/logo.png" alt="Creative Hydraulics Logo" class="w-14 h-14 md:w-18 md:h-18 object-contain" />
                        </div>
                        <div class="flex flex-col translate-y-0.5">
                            <span
                                class="text-lg md:text-xl font-Michroma font-bold bg-gradient-to-r from-blue-900 to-blue-700 bg-clip-text text-transparent tracking-wide">
                                Creative Hydraulics
                            </span>
                            <span
                                class="text-xs mx-0.5 -translate-y-1 font-Jura  md:text-sm text-gray-600 tracking-wider">Complete Pressure Testing Solutions</span>
                        </div>
                    </a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-2">
                    <a href="index.html"
                        class="nav-link nav-link-active px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Home</a>
                    
                    <a href="pages/about.html"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">About Us</a>
                    <a href="#certifications"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Certifications</a>
                    
                    <!-- Products Dropdown -->
                    <div class="dropdown-parent">
                        <button class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px] flex items-center">
                            Products
                            <i class="fas fa-chevron-down ml-1 text-xs transition-transform dropdown-parent:hover:rotate-180"></i>
                        </button>
                        <div class="nav-dropdown absolute w-[650px] rounded-xl overflow-hidden">
                            <div class="py-4">
                                <!-- View All Products Link -->
                                <div class="px-5 py-3 border-b border-gray-100 mb-3">
                                    <a href="pages/products.html" class="flex items-center text-gray-700 font-Jura font-medium text-sm hover:bg-blue-50 rounded-lg p-2 transition-all duration-200">
                                        <div class="w-8 h-8 bg-[#D22B2B]/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-th-large text-[#D22B2B] text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">View All Products</div>
                                            <div class="text-xs text-gray-500">Complete product overview</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="grid grid-cols-2 gap-0">
                                    <!-- Left Column -->
                                    <div class="space-y-1">
                                        <a href="pages/manual.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                                <i class="fas fa-hand-paper text-blue-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900">Manual Systems</div>
                                                <div class="text-xs text-gray-500">Precision manual control</div>
                                            </div>
                                        </a>
                                        <a href="pages/semi.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                                <i class="fas fa-cogs text-blue-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900">Semi-Automatic</div>
                                                <div class="text-xs text-gray-500">PLC controlled systems</div>
                                            </div>
                                        </a>
                                        <a href="pages/auto.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                                <i class="fas fa-robot text-blue-600 text-sm"></i>
                                            </div>
                                            <div>
                                                <div class="font-semibold text-gray-900">Fully Automatic</div>
                                                <div class="text-xs text-gray-500">AI-driven automation</div>
                                            </div>
                                        </a>
                                    <a href="pages/hts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-cog text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Horizontal Test Stand</div>
                                                <div class="text-xs text-gray-500">HTS - Pressure testing</div>
                                        </div>
                                    </a>
                                    <a href="pages/vts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-arrows-alt-v text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Vertical Test Stand</div>
                                                <div class="text-xs text-gray-500">VTS - Vertical testing</div>
                                        </div>
                                    </a>
                                    </div>
                                    
                                    <!-- Right Column -->
                                    <div class="space-y-1">
                                    <a href="pages/mshts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-sitemap text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                                <div class="font-semibold text-gray-900">Multi-Station Test</div>
                                                <div class="text-xs text-gray-500">MSHTS - Multi-station</div>
                                        </div>
                                    </a>
                                    <a href="pages/fsts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-fire-extinguisher text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Fire Safe Test Stand</div>
                                                <div class="text-xs text-gray-500">Fire safety testing</div>
                                        </div>
                                    </a>
                                    <a href="pages/cts.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-snowflake text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Cryogenic Test Stand</div>
                                            <div class="text-xs text-gray-500">Low temperature testing</div>
                                        </div>
                                    </a>
                                    <a href="pages/fet.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-search text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Fugitive Emission Test</div>
                                                <div class="text-xs text-gray-500">FET - Emission testing</div>
                                        </div>
                                    </a>
                                    <a href="pages/hp.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-compress-arrows-alt text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Hydraulic Presses</div>
                                                <div class="text-xs text-gray-500">Industrial pressing</div>
                                        </div>
                                    </a>
                                    <a href="pages/spm.html" class="dropdown-item flex items-center px-5 py-3 text-gray-700 font-Jura font-medium text-sm">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-tools text-blue-600 text-sm"></i>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Special Purpose Machine</div>
                                            <div class="text-xs text-gray-500">SPM - Custom solutions</div>
                                        </div>
                                    </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <a href="pages/infrastructure.html"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Infrastructure</a>
                    <a href="pages/contact.html"
                        class="nav-link text-gray-800 hover:text-blue-900 px-3 py-2 font-Jura font-bold tracking-wide text-[15px]">Contact Us</a>
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="p-2 rounded-xl hover:bg-blue-50/80 transition-colors group">
                        <svg class="h-6 w-6 text-blue-900 group-hover:text-blue-700 transition-colors" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden font-Jura md:hidden">
                <div class="fixed inset-0 bg-black/20 z-40 transition-opacity duration-300 opacity-0"
                    id="mobile-menu-backdrop"></div>
                <div class="fixed inset-y-0 right-0 w-[280px] bg-white shadow-2xl z-50 transition-transform duration-300 transform translate-x-full mobile-menu-container flex flex-col"
                    id="mobile-menu-content">
                    <!-- Mobile Menu Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-100 flex-shrink-0">
                        <span class="text-lg font-display font-semibold text-blue-900">Menu</span>
                        <button id="mobile-menu-close" class="p-2 rounded-xl hover:bg-blue-50/80 transition-colors">
                            <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile Menu Items - Scrollable Area -->
                    <div class="flex-1 overflow-y-auto py-3" style="max-height: calc(100vh - 180px);">
                        <a href="index.html"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-blue-900 bg-blue-50/50">
                            <i class="fas fa-home w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Home</span>
                        </a>
                        
                        <a href="pages/about.html"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-gray-800 hover:bg-blue-50/50">
                            <i class="fas fa-info-circle w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">About Us</span>
                        </a>
                        <a href="#certifications"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-gray-800 hover:bg-blue-50/50">
                            <i class="fas fa-certificate w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Certifications</span>
                        </a>
                        
                        <!-- Mobile Products Dropdown -->
                        <div class="mobile-menu-item">
                            <button class="flex items-center justify-between w-full px-6 py-3.5 text-gray-800 hover:bg-blue-50/50" onclick="toggleMobileDropdown()">
                                <div class="flex items-center">
                            <i class="fas fa-cube w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Products</span>
                                </div>
                                <i class="fas fa-chevron-down text-gray-500 transition-transform" id="mobileDropdownIcon"></i>
                            </button>
                            <div class="hidden bg-gray-50 border-l-2 border-blue-200 ml-6" id="mobileProductsDropdown">
                                <a href="pages/products.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm border-b border-gray-200">
                                    <i class="fas fa-th-large text-[#D22B2B] mr-2 w-4"></i>
                                    View All Products
                                </a>
                                <a href="pages/manual.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-hand-paper text-blue-600 mr-2 w-4"></i>
                                    Manual Systems
                                </a>
                                <a href="pages/semi.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-cogs text-blue-600 mr-2 w-4"></i>
                                    Semi-Automatic Systems
                                </a>
                                <a href="pages/auto.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-robot text-blue-600 mr-2 w-4"></i>
                                    Fully Automatic Systems
                                </a>
                                <a href="pages/hts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-cog text-blue-600 mr-2 w-4"></i>
                                    Horizontal Test Stand (HTS)
                                </a>
                                <a href="pages/vts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-arrows-alt-v text-blue-600 mr-2 w-4"></i>
                                    Vertical Test Stand (VTS)
                                </a>
                                <a href="pages/mshts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-sitemap text-blue-600 mr-2 w-4"></i>
                                    Multi-Station Test Stand (MSHTS)
                                </a>
                                <a href="pages/fsts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-fire-extinguisher text-blue-600 mr-2 w-4"></i>
                                    Fire Safe Test Stand
                                </a>
                                <a href="pages/cts.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-snowflake text-blue-600 mr-2 w-4"></i>
                                    Cryogenic Test Stand
                                </a>
                                <a href="pages/fet.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-search text-blue-600 mr-2 w-4"></i>
                                    Fugitive Emission Test (FET)
                                </a>
                                <a href="pages/hp.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-compress-arrows-alt text-blue-600 mr-2 w-4"></i>
                                    Hydraulic Presses
                                </a>
                                <a href="pages/spm.html" class="block px-6 py-2.5 text-gray-700 hover:bg-blue-50 font-Jura text-sm">
                                    <i class="fas fa-tools text-blue-600 mr-2 w-4"></i>
                                    Special Purpose Machine (SPM)
                                </a>
                            </div>
                        </div>
                        
                        <a href="pages/infrastructure.html"
                            class="mobile-menu-item flex items-center px-6 py-3.5 text-gray-800 hover:bg-blue-50/50">
                            <i class="fas fa-building w-6 text-blue-900/70"></i>
                            <span class="ml-3 font-Jura font-bold tracking-wide">Infrastructure</span>
                        </a>
                    </div>

                    <!-- Mobile Menu Footer -->
                    <div class="flex-shrink-0 p-4 border-t border-gray-100">
                        <a href="pages/contact.html"
                            class="block text-center bg-gradient-to-r from-blue-900 to-blue-700 text-white py-3.5 px-6 rounded-xl hover:shadow-lg transition-all font-Jura font-semibold tracking-wide">
                            Contact Us
                        </a>
                        <div class="mt-4 flex items-center justify-center space-x-4 py-2">
                            <a href="mailto:<EMAIL>"
                                class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-envelope text-lg"></i>
                            </a>
                            <div class="h-4 w-px bg-gray-200"></div>
                            <a href="tel:09449024839" class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-phone text-lg"></i>
                            </a>
                            <div class="h-4 w-px bg-gray-200"></div>
                            <a href="tel:+918971587348" class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-phone text-lg"></i>
                            </a>
                            <div class="h-4 w-px bg-gray-200"></div>
                            <a href="#location" class="text-gray-600 hover:text-blue-900 mobile-icon">
                                <i class="fas fa-map-marker-alt text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div id="home" class="relative h-[85vh] overflow-hidden bg-white">
        <!-- Video/Image Carousel -->
        <div class="absolute inset-0 w-full h-full z-0">
            <div id="hero-carousel" class="relative w-full h-full overflow-hidden">
                <div class="carousel-container" style="display: flex; width: 600%; height: 100%; transition: transform 1s ease-in-out;">
                    <!-- Video Slide -->
                    <div class="carousel-slide" style="width: 16.666%; height: 100%; flex-shrink: 0; position: relative;">
                    <!-- Desktop Video -->
                    <video class="w-full h-full object-cover hidden md:block" autoplay loop muted playsinline
                        poster="https://images.unsplash.com/photo-1631651363531-ee8be8685c8e?w=1920">
                        <source src="assets/videos/bg-vid.mp4" type="video/mp4">
                    </video>
                    <!-- Mobile Video -->
                    <video class="w-full h-full object-cover block md:hidden" autoplay loop muted playsinline
                        poster="https://images.unsplash.com/photo-1631651363531-ee8be8685c8e?w=1920">
                        <source src="assets/videos/mob-bg.mp4" type="video/mp4">
                    </video>
                </div>
                    
                    <!-- Image Slides -->
                    <!-- <div class="carousel-slide" style="width: 16.666%; height: 100%; flex-shrink: 0; position: relative;">
                        <img src="assets/images/cylinder.jpg" alt="Hydraulic Cylinder" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black/20"></div>
                    </div>
                    
                    <div class="carousel-slide" style="width: 16.666%; height: 100%; flex-shrink: 0; position: relative;">
                        <img src="assets/images/hydrulic_pump.jpg" alt="Hydraulic Pump" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black/20"></div>
                    </div>
                    
                    <div class="carousel-slide" style="width: 16.666%; height: 100%; flex-shrink: 0; position: relative;">
                        <img src="assets/images/hcmYN.jpg" alt="Hydraulic Equipment" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black/20"></div>
        </div>

                    <div class="carousel-slide" style="width: 16.666%; height: 100%; flex-shrink: 0; position: relative;">
                        <img src="assets/images/reference.jpg" alt="Reference Equipment" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black/20"></div>
                    </div>
                    
                    <div class="carousel-slide" style="width: 16.666%; height: 100%; flex-shrink: 0; position: relative;">
                        <img src="assets/images/lol.jpg" alt="Industrial Equipment" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black/20"></div>
                    </div> -->
                </div>
            </div>
        </div>

        <!-- Carousel Controls -->
        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
            <div class="flex items-center space-x-3 bg-black/10 backdrop-blur-sm rounded-full px-3 py-1.5">
                <!-- Play/Pause Button -->
                <button id="playPauseBtn" class="w-6 h-6 bg-[#D22B2B]/80 hover:bg-[#D22B2B] rounded-full flex items-center justify-center transition-colors duration-300" aria-label="Play/Pause">
                    <i id="playPauseIcon" class="fas fa-pause text-white" style="font-size: 10px;"></i>
                </button>
                
                <!-- Dots Navigation -->
                <div id="carouselDots" class="flex space-x-1.5">
                    <!-- Dots will be generated dynamically -->
                </div>
            </div>
        </div>

        <script>
            // Simple, reliable carousel with drag support, dots, and play/pause
            document.addEventListener('DOMContentLoaded', function() {
                const carouselContainer = document.querySelector('.carousel-container');
                const slides = document.querySelectorAll('.carousel-slide');
                const dotsContainer = document.getElementById('carouselDots');
                const playPauseBtn = document.getElementById('playPauseBtn');
                const playPauseIcon = document.getElementById('playPauseIcon');
                
                let currentSlide = 0;
                const totalSlides = slides.length;
                
                // Find video in first slide
                const video = slides[0].querySelector('video');
                
                // Drag/swipe variables
                let isDragging = false;
                let startX = 0;
                let currentX = 0;
                let initialTranslate = 0;
                
                // Auto-advance variables
                let autoAdvanceInterval;
                let isPlaying = true;
                let isPaused = false;
                
                // Create navigation dots
                function createDots() {
                    dotsContainer.innerHTML = '';
                    for (let i = 0; i < totalSlides; i++) {
                        const dot = document.createElement('button');
                        dot.className = `w-1.5 h-1.5 rounded-full transition-colors duration-300 ${i === 0 ? 'bg-[#D22B2B]/90' : 'bg-white/30 hover:bg-white/50'}`;
                        dot.setAttribute('aria-label', `Go to slide ${i + 1}`);
                        dot.addEventListener('click', () => goToSlideWithDot(i));
                        dotsContainer.appendChild(dot);
                    }
                }
                
                // Update active dot
                function updateDots() {
                    const dots = dotsContainer.querySelectorAll('button');
                    dots.forEach((dot, index) => {
                        if (index === currentSlide) {
                            dot.className = 'w-1.5 h-1.5 rounded-full transition-colors duration-300 bg-[#D22B2B]/90';
                        } else {
                            dot.className = 'w-1.5 h-1.5 rounded-full transition-colors duration-300 bg-white/30 hover:bg-white/50';
                        }
                    });
                }
                
                // Function to move to specific slide
                function goToSlide(slideIndex) {
                    const translateX = -(slideIndex * (100 / totalSlides));
                    carouselContainer.style.transform = `translateX(${translateX}%)`;
                    carouselContainer.style.transition = 'transform 1s ease-in-out';
                    currentSlide = slideIndex;
                    updateDots();
                }
                
                // Function to go to slide via dot click
                function goToSlideWithDot(slideIndex) {
                    isPaused = true;
                    goToSlide(slideIndex);
                    handleVideoPlayback(slideIndex);
                    
                    // Resume auto-advance after 3 seconds if playing
                    if (isPlaying) {
                        setTimeout(() => {
                            isPaused = false;
                        }, 3000);
                    }
                }
                
                // Function to handle video playback
                function handleVideoPlayback(slideIndex) {
                    if (video) {
                        if (slideIndex === 0) {
                            video.currentTime = 0;
                            video.play();
                        } else {
                            video.pause();
                        }
                    }
                }
                
                // Function to go to next slide
                function nextSlide() {
                    if (!isPaused && isPlaying) {
                        currentSlide = (currentSlide + 1) % totalSlides;
                        goToSlide(currentSlide);
                        handleVideoPlayback(currentSlide);
                    }
                }
                
                // Function to go to previous slide
                function prevSlide() {
                    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                    goToSlide(currentSlide);
                handleVideoPlayback(currentSlide);
                }
                
                // Function to toggle play/pause
                function togglePlayPause() {
                    isPlaying = !isPlaying;
                    
                    if (isPlaying) {
                        playPauseIcon.className = 'fas fa-pause text-white';
                        playPauseIcon.style.fontSize = '10px';
                        isPaused = false;
                        startAutoAdvance();
                    } else {
                        playPauseIcon.className = 'fas fa-play text-white';
                        playPauseIcon.style.fontSize = '10px';
                        isPaused = true;
                        stopAutoAdvance();
                    }
                }
                
                // Function to start auto-advance
                function startAutoAdvance() {
                    if (autoAdvanceInterval) clearInterval(autoAdvanceInterval);
                    autoAdvanceInterval = setInterval(nextSlide, 7500);
                }
                
                // Function to stop auto-advance
                function stopAutoAdvance() {
                    if (autoAdvanceInterval) {
                        clearInterval(autoAdvanceInterval);
                        autoAdvanceInterval = null;
                    }
                }
                
                // Function to set transform without transition
                function setSliderPosition() {
                    const translateX = initialTranslate + currentX;
                    carouselContainer.style.transform = `translateX(${translateX}px)`;
                    carouselContainer.style.transition = 'none';
                }
                
                // Function to handle drag start
                function dragStart(e) {
                    isDragging = true;
                    isPaused = true;
                    
                    startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                    initialTranslate = -(currentSlide * (window.innerWidth));
                    
                    carouselContainer.style.cursor = 'grabbing';
                    e.preventDefault();
                }
                
                // Function to handle drag move
                function dragMove(e) {
                    if (!isDragging) return;
                    
                    e.preventDefault();
                    
                    const clientX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
                    currentX = clientX - startX;
                    
                    setSliderPosition();
                }
                
                // Function to handle drag end
                function dragEnd() {
                    if (!isDragging) return;
                    
                    isDragging = false;
                    carouselContainer.style.cursor = 'grab';
                    
                    const movedBy = currentX;
                    const threshold = window.innerWidth / 4; // 25% of screen width
                    
                    if (movedBy < -threshold && currentSlide < totalSlides - 1) {
                        // Swiped left - go to next slide
                        nextSlide();
                    } else if (movedBy > threshold && currentSlide > 0) {
                        // Swiped right - go to previous slide
                        prevSlide();
                    } else {
                        // Snap back to current slide
                        goToSlide(currentSlide);
                    }
                    
                    // Reset values
                    currentX = 0;
                    initialTranslate = 0;
                    
                    // Resume auto-advance after 2 seconds if playing
                    if (isPlaying) {
                        setTimeout(() => {
                            isPaused = false;
                        }, 2000);
                    }
                }
                
                // Event listeners
                
                // Play/Pause button
                playPauseBtn.addEventListener('click', togglePlayPause);
                
                // Mouse events
                carouselContainer.addEventListener('mousedown', dragStart);
                document.addEventListener('mousemove', dragMove);
                document.addEventListener('mouseup', dragEnd);
                
                // Touch events
                carouselContainer.addEventListener('touchstart', dragStart, { passive: false });
                document.addEventListener('touchmove', dragMove, { passive: false });
                document.addEventListener('touchend', dragEnd);
                
                // Prevent context menu on right click
                carouselContainer.addEventListener('contextmenu', e => e.preventDefault());
                
                // Prevent drag on images
                carouselContainer.addEventListener('dragstart', e => e.preventDefault());
                
                // Set initial cursor
                carouselContainer.style.cursor = 'grab';
                
                // Pause auto-advance on hover
                carouselContainer.addEventListener('mouseenter', () => {
                    if (isPlaying) isPaused = true;
                });
                
                carouselContainer.addEventListener('mouseleave', () => {
                    if (!isDragging && isPlaying) {
                        isPaused = false;
                    }
                });
                
                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') {
                        e.preventDefault();
                        prevSlide();
                    } else if (e.key === 'ArrowRight') {
                        e.preventDefault();
                        nextSlide();
                    } else if (e.key === ' ') {
                        e.preventDefault();
                        togglePlayPause();
                    }
                });
                
                // Initialize carousel
                createDots();
                goToSlide(0);
                handleVideoPlayback(0);
                startAutoAdvance();
            });
        </script>
    </div>

    <!-- About Us Section -->
    <section id="about" class="py-24 bg-white">
        <div class="container mx-auto px-6">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <span class="text-[#D22B2B] font-bold tracking-wider text-sm mb-3 block font-Jura">ABOUT US</span>
                <h2 class="text-4xl font-Michroma font-bold text-gray-900 mb-4">About Creative Hydraulics</h2>
                <div class="w-24 h-1 bg-[#D22B2B] rounded-full   mx-auto"></div>
                <p class="text-xl  text-gray-600 font-Jura mt-6 max-w-2xl mx-auto">
                    Delivering excellence in hydraulic manufacturing since inception, with a commitment to innovation
                    and quality.
                </p>
            </div>

            <!-- About Content -->
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <!-- Left Column - Text Content -->
                <div class="space-y-6">
                    <div class="bg-gradient-to-r from-blue-50 to-transparent p-8 rounded-2xl">
                        <h3 class="text-2xl font-Michroma font-bold text-blue-900 mb-4">Leading Hydraulic Equipment
                            Manufacturer</h3>
                        <p class="text-gray-700 font-Jura leading-relaxed mb-6">
                            Based in Belagavi, Karnataka, Creative Hydraulics has established itself as a trusted
                            manufacturer of premium hydraulic equipment. Under the leadership of our Managing Director,
                            <span class="font-bold text-[#000]">Basavaraj B. Tonannavar</span>, we've built a reputation for
                            delivering innovative hydraulic solutions that meet global standards.
                        </p>
                        <p class="text-gray-700 font-Jura leading-relaxed">
                            Our commitment to excellence has earned us partnerships with industry leaders like Reliance
                            and L&T, while our global reach extends to major markets including USA, Saudi Arabia, Japan,
                            and beyond.
                        </p>
                    </div>


                </div>

                <!-- Right Column - Image Gallery -->
                <div class="space-y-6">


                    <!-- Leadership Image -->
                    <div class="relative h-96 rounded-2xl overflow-hidden shadow-xl">
                        <img src="https://imgs.search.brave.com/fYcBpumBOVkrKIE2hv0cDqjeI12XukSTv3Akyg3wY6I/rs:fit:860:0:0:0/g:ce/aHR0cHM6Ly93d3cu/amdpLmFjLmluL2Fz/c2V0cy9pbWFnZXMv/Y2hhaXJtYW5fc2ly/XzA2LTEwLTIwMjMu/anBn"
                            alt="Creative Hydraulics Facility" class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-blue-600/20"></div>
                            <div class="absolute bottom-0 left-0 right-0 p-1">
                                <p class="text-white bg-blue-600/60 p-2 rounded-xl font-Michroma font-bold">Basavaraj B. Tonannavar</p>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Products Section -->
    <section id="products" class="geometric-bg min-h-screen flex items-center py-20 relative overflow-hidden">
        <!-- Pattern Overlay -->
        <div class="pattern-overlay"></div>

        <div class="container mx-auto px-6 relative w-full z-10">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <span class="text-[#D22B2B] font-semibold tracking-wider text-sm mb-2 block font-Jura uppercase">Our Products</span>
                <h2 class="text-4xl md:text-5xl font-Michroma font-bold text-gray-900 mb-4">Featured Solutions</h2>
                <div class="w-20 h-0.5 bg-[#D22B2B] mx-auto mb-6"></div>
                <p class="text-xl text-gray-600 font-Jura max-w-3xl mx-auto leading-relaxed">
                    Precision-engineered hydraulic solutions for diverse industrial applications
                </p>
            </div>

            <!-- Desktop Grid - 3 Cards per row -->
            <div class="hidden lg:grid grid-cols-3 gap-8 max-w-7xl mx-auto px-4 py-6" id="products-desktop">
                <!-- Product cards will be rendered here by JavaScript -->
            </div>

            <!-- Tablet View - 2 Cards per row -->
            <div class="hidden md:grid lg:hidden grid-cols-2 gap-8 max-w-6xl mx-auto px-8 py-6" id="products-tablet">
                <!-- Product cards will be rendered here by JavaScript -->
            </div>

            <!-- Mobile Carousel -->
            <div class="block md:hidden">
                <div class="swiper productsSwiper max-w-lg mx-auto px-4">
                    <div class="swiper-wrapper" id="products-mobile">
                        <!-- Product slides will be rendered here by JavaScript -->
                    </div>
                    <div class="swiper-pagination mt-8"></div>
                </div>
            </div>
        </div>
    </section>


    <!-- Script file of Product cards -->
    <script>
    // Product data
    const productData = {
        manual: {
            title: 'Manual',
            category: 'Manual',
            image: 'https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Manual.png',
            description: 'High-performance manual hydraulic systems designed for optimal control and precision in demanding industrial applications. Built with robust engineering for reliability and durability.',
            link: 'pages/manual.html',
            linkText: 'Learn More'
        },
        'semi-auto': {
            title: 'Semi-Automatic',
            category: 'Semi-Auto',
            image: 'https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Semi_aut.png',
            description: 'Precision-engineered semi-automatic hydraulic systems for reliable force transmission in manufacturing processes. Combines manual control with automated features.',
            link: 'pages/semi.html',
            linkText: 'Learn More'
        },
        'fully-auto': {
            title: 'Fully Automatic',
            category: 'Automatic',
            image: 'https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/Fully_aut.png',
            description: 'Advanced fully automatic hydraulic flow control solutions for precise system regulation. Features intelligent control systems and real-time monitoring capabilities.',
            link: 'pages/auto.html',
            linkText: 'Learn More'
        },
        spm: {
            title: 'Special Purpose Machine (SPM)',
            category: 'SPM',
            image: 'https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/SPM.png',
            description: 'Custom-designed machines built to perform specific tasks in manufacturing, production, or testing environments. Tailored for particular applications to increase efficiency and precision.',
            link: 'pages/spm.html',
            linkText: 'Learn More'
        },
        multistation: {
            title: 'Multistation Test Stand',
            category: 'Testing',
            image: 'https://raw.githubusercontent.com/CreativeHyd/Portfolio/refs/heads/main/MultiStation.png',
            description: 'Fixed hydrostatic testing setup with multiple test bays for conducting pressure tests on various components. Advanced automation and capacity for wide range of components.',
            link: 'pages/mshts.html',
            linkText: 'Learn More'
        },
        view_all: {
            title: 'View All Products',
            category: 'All Products',
            image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=600&fit=crop',
            description: 'Explore our complete range of hydraulic solutions including manual systems, automatic systems, test stands, and specialized equipment. Discover innovative solutions for your industrial needs.',
            link: 'pages/products.html',
            linkText: 'View All',
            isSpecial: true
        }
    };

    // Render product cards
    function renderProductCards() {
        const productKeys = ['manual','semi-auto','fully-auto','spm','multistation','view_all'];
        
        // Desktop
        const desktop = document.getElementById('products-desktop');
        if (desktop) {
            desktop.innerHTML = '';
            productKeys.forEach(key => {
                const p = productData[key];
                const card = document.createElement('div');
                card.className = 'group product-card overflow-visible transition-all duration-500 flex flex-col cursor-pointer';
                card.setAttribute('data-product', key);
                card.onclick = () => {
                    if (p.link !== '#') {
                        window.location.href = p.link;
                    }
                };
                
                if (p.isSpecial) {
                    // Special layout for "View All Products" card
                card.innerHTML = `
                        <div class=\"relative h-full overflow-hidden product-image-container bg-gray-100 group-hover:scale-105 transition-transform duration-500\">
                            <img src=\"${p.image}\" alt=\"${p.title}\" class=\"w-full h-full object-cover\">
                            <div class=\"absolute inset-0 bg-gradient-to-r from-[#0047AB]/80 to-[#D22B2B]/80\"></div>
                            <div class=\"absolute inset-0 flex flex-col items-center justify-center text-white text-center p-6\">
                                <div class=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 group-hover:bg-white/30 transition-all duration-300\">
                                    <i class=\"fas fa-th-large text-2xl\"></i>
                                </div>
                                <h3 class=\"text-2xl font-Michroma font-bold mb-3 group-hover:scale-105 transition-transform duration-300\">${p.title}</h3>
                                <p class=\"text-white/90 font-Jura text-sm mb-4 leading-relaxed max-w-xs\">${p.description}</p>
                                <div class=\"inline-flex items-center bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-Jura font-medium transition-all duration-300 group-hover:scale-105\">
                                    <span>${p.linkText}</span>
                                    <i class=\"fas fa-arrow-right ml-2 text-sm\"></i>
                                </div>
                            </div>
                        </div>`;
                } else {
                    // Regular layout for other product cards
                    card.innerHTML = `
                        <div class=\"relative h-64 overflow-hidden product-image-container bg-gray-100\">
                        <img src=\"${p.image}\" alt=\"${p.title}\" class=\"w-full h-full object-cover\">
                            <div class=\"absolute top-4 right-4\">
                                <span class=\"px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium\">${p.category}</span>
                        </div>
                    </div>
                        <div class=\"product-card-content flex-1 flex flex-col justify-between p-6 bg-white\">
                        <div class=\"flex-1\">
                                <h3 class=\"text-xl font-Michroma font-bold text-gray-900 mb-4\">${p.title}</h3>
                                <p class=\"text-gray-600 font-Jura mb-4 text-base leading-relaxed line-clamp-3\">${p.description}</p>
                                <div class=\"flex flex-wrap gap-2 mb-4\">
                                    <span class=\"px-3 py-1 bg-gray-100 text-gray-700 text-sm font-Jura font-medium border border-gray-200\">Premium Quality</span>
                                    <span class=\"px-3 py-1 bg-gray-100 text-gray-700 text-sm font-Jura font-medium border border-gray-200\">ISO Certified</span>
                            </div>
                        </div>
                            <div class=\"flex items-center justify-between pt-4 border-t border-gray-200\">
                                <span class=\"inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-base\">
                                <span>${p.linkText}</span>
                                    <i class=\"fas fa-arrow-right ml-2 text-sm\"></i>
                            </span>
                        </div>
                    </div>`;
                }
                desktop.appendChild(card);
            });
        }
        // Tablet
        const tablet = document.getElementById('products-tablet');
        if (tablet) {
            tablet.innerHTML = '';
            
            productKeys.forEach(key => {
                const p = productData[key];
                const card = document.createElement('div');
                card.className = 'group product-card overflow-visible transition-all duration-500 flex flex-col cursor-pointer';
                card.setAttribute('data-product', key);
                card.onclick = () => {
                    if (p.link !== '#') {
                        window.location.href = p.link;
                    }
                };
                
                if (p.isSpecial) {
                    // Special layout for "View All Products" card
                card.innerHTML = `
                        <div class=\"relative h-full overflow-hidden product-image-container bg-gray-100 group-hover:scale-105 transition-transform duration-500\">
                        <img src=\"${p.image}\" alt=\"${p.title}\" class=\"w-full h-full object-cover\">
                            <div class=\"absolute inset-0 bg-gradient-to-r from-[#0047AB]/80 to-[#D22B2B]/80\"></div>
                            <div class=\"absolute inset-0 flex flex-col items-center justify-center text-white text-center p-4\">
                                <div class=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3 group-hover:bg-white/30 transition-all duration-300\">
                                    <i class=\"fas fa-th-large text-xl\"></i>
                        </div>
                                <h3 class=\"text-xl font-Michroma font-bold mb-2 group-hover:scale-105 transition-transform duration-300\">${p.title}</h3>
                                <p class=\"text-white/90 font-Jura text-xs mb-3 leading-relaxed max-w-xs\">${p.description}</p>
                                <div class=\"inline-flex items-center bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg font-Jura font-medium transition-all duration-300 group-hover:scale-105 text-sm\">
                                <span>${p.linkText}</span>
                                <i class=\"fas fa-arrow-right ml-2 text-xs\"></i>
                                </div>
                        </div>
                    </div>`;
                } else {
                    // Regular layout for other product cards
                card.innerHTML = `
                        <div class=\"relative h-60 overflow-hidden product-image-container bg-gray-100\">
                        <img src=\"${p.image}\" alt=\"${p.title}\" class=\"w-full h-full object-cover\">
                        <div class=\"absolute top-4 right-4\">
                                <span class=\"px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium\">${p.category}</span>
                        </div>
                    </div>
                    <div class=\"product-card-content flex-1 flex flex-col justify-between p-6 bg-white\">
                        <div class=\"flex-1\">
                            <h3 class=\"text-lg font-Michroma font-bold text-gray-900 mb-3\">${p.title}</h3>
                            <p class=\"text-gray-600 font-Jura mb-4 text-sm leading-relaxed line-clamp-3\">${p.description}</p>
                            <div class=\"flex flex-wrap gap-2 mb-4\">
                                <span class=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200\">Premium Quality</span>
                                <span class=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200\">ISO Certified</span>
                            </div>
                        </div>
                        <div class=\"flex items-center justify-between pt-3 border-t border-gray-200\">
                            <span class=\"inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-sm\">
                                <span>${p.linkText}</span>
                                <i class=\"fas fa-arrow-right ml-2 text-xs\"></i>
                            </span>
                        </div>
                    </div>`;
                }
                tablet.appendChild(card);
            });
        }
        // Mobile
        const mobile = document.getElementById('products-mobile');
        if (mobile) {
            mobile.innerHTML = '';
            productKeys.forEach(key => {
                const p = productData[key];
                const slide = document.createElement('div');
                slide.className = 'swiper-slide';
                const card = document.createElement('div');
                card.className = 'product-card overflow-visible transition-all duration-500 flex flex-col cursor-pointer';
                card.setAttribute('data-product', key);
                card.onclick = () => {
                    if (p.link !== '#') {
                        window.location.href = p.link;
                    }
                };
                
                if (p.isSpecial) {
                    // Special layout for "View All Products" card
                card.innerHTML = `
                        <div class=\"relative h-full overflow-hidden product-image-container bg-gray-100 group-hover:scale-105 transition-transform duration-500\">
                            <img src=\"${p.image}\" alt=\"${p.title}\" class=\"w-full h-full object-cover\">
                            <div class=\"absolute inset-0 bg-gradient-to-r from-[#0047AB]/80 to-[#D22B2B]/80\"></div>
                            <div class=\"absolute inset-0 flex flex-col items-center justify-center text-white text-center p-6\">
                                <div class=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 group-hover:bg-white/30 transition-all duration-300\">
                                    <i class=\"fas fa-th-large text-2xl\"></i>
                                </div>
                                <h3 class=\"text-xl font-Michroma font-bold mb-3 group-hover:scale-105 transition-transform duration-300\">${p.title}</h3>
                                <p class=\"text-white/90 font-Jura text-sm mb-4 leading-relaxed max-w-xs\">${p.description}</p>
                                <div class=\"inline-flex items-center bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-Jura font-medium transition-all duration-300 group-hover:scale-105\">
                                    <span>${p.linkText}</span>
                                    <i class=\"fas fa-arrow-right ml-2 text-sm\"></i>
                                </div>
                            </div>
                        </div>`;
                } else {
                    // Regular layout for other product cards
                    card.innerHTML = `
                        <div class=\"relative h-72 overflow-hidden product-image-container bg-gray-100\">
                        <img src=\"${p.image}\" alt=\"${p.title}\" class=\"w-full h-full object-cover\">
                        <div class=\"absolute top-4 right-4\">
                                <span class=\"px-3 py-1 bg-[#0047AB] text-white text-sm font-Jura font-medium\">${p.category}</span>
                        </div>
                    </div>
                    <div class=\"product-card-content flex-1 flex flex-col justify-between p-6 bg-white\">
                        <div class=\"flex-1\">
                            <h3 class=\"text-lg font-Michroma font-bold text-gray-900 mb-3\">${p.title}</h3>
                            <p class=\"text-gray-600 font-Jura mb-4 text-sm leading-relaxed line-clamp-3\">${p.description}</p>
                            <div class=\"flex flex-wrap gap-2 mb-4\">
                                <span class=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200\">Premium</span>
                                <span class=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200\">Certified</span>
                            </div>
                        </div>
                        <div class=\"flex items-center justify-between pt-3 border-t border-gray-200\">
                            <span class=\"inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-sm\">
                                <span>${p.linkText}</span>
                                <i class=\"fas fa-arrow-right ml-2 text-xs\"></i>
                            </span>
                        </div>
                    </div>`;
                }
                slide.appendChild(card);
                mobile.appendChild(slide);
            });
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        renderProductCards();
    });
    </script>

    <!-- Global Export Section -->
    <section id="global" class="relative py-16 bg-white overflow-hidden">
        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 opacity-[0.02]">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="globalPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
                        <circle cx="50" cy="50" r="1" fill="#0047AB"/>
                        <circle cx="25" cy="25" r="0.5" fill="#D22B2B"/>
                        <circle cx="75" cy="75" r="0.5" fill="#D22B2B"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#globalPattern)"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 max-w-6xl relative z-10">
            <!-- Section Header -->
            <div class="text-center mb-12">
                <span class="text-[#D22B2B] font-Jura font-semibold tracking-wider text-sm mb-2 block uppercase">Global Network</span>
                <h2 class="text-3xl md:text-4xl font-Michroma font-bold text-gray-900 mb-3">International Exports</h2>
                <div class="w-16 h-0.5 bg-[#D22B2B] mx-auto mb-4"></div>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto font-Jura">
                    Delivering precision-engineered hydraulic solutions to leading industrial markets across continents
                </p>
            </div>

            <!-- Main Export Display -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                <div class="grid lg:grid-cols-2 gap-0">
                    <!-- Left Side - Professional Stats -->
                    <div class="p-6 lg:p-8 bg-gradient-to-br from-slate-50 to-blue-50/30 relative">
                        <!-- Decorative Elements -->
                        <div class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full"></div>
                        <div class="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-[#D22B2B]/5 to-transparent rounded-tr-full"></div>
                        
                        <div class="relative z-10">
                            <!-- Header -->
                            <div class="mb-6">
                                <div class="flex items-center mb-3">
                                    <div class="w-8 h-8 rounded-lg bg-[#0047AB]/10 flex items-center justify-center mr-3">
                                        <i class="fas fa-globe text-[#0047AB] text-sm"></i>
                                    </div>
                                    <h3 class="text-xl font-Michroma font-bold text-gray-900">Global Presence</h3>
                                </div>
                                <p class="text-gray-600 font-Jura text-sm">
                                    Trusted by industry leaders worldwide for superior hydraulic testing solutions
                                </p>
                        </div>

                            <!-- Key Metrics -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="text-center p-3 bg-white/60 rounded-lg border border-white/40">
                                    <div class="text-2xl font-Michroma font-bold text-[#0047AB] mb-1">5+</div>
                                    <div class="text-xs font-Jura text-gray-600 font-medium">Countries Served</div>
                                </div>
                                <div class="text-center p-3 bg-white/60 rounded-lg border border-white/40">
                                    <div class="text-2xl font-Michroma font-bold text-[#D22B2B] mb-1">100+</div>
                                    <div class="text-xs font-Jura text-gray-600 font-medium">Projects Delivered</div>
                                        </div>
                                    </div>

                            <!-- Quality Assurance -->
                            <div class="bg-white/80 rounded-lg p-4 border border-white/60">
                                <div class="flex items-start space-x-3">
                                    <div class="w-6 h-6 rounded-lg bg-[#0047AB]/10 flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-certificate text-[#0047AB] text-xs"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-Michroma font-semibold text-gray-900 mb-1 text-sm">Export Excellence</h4>
                                        <p class="text-gray-600 font-Jura text-xs leading-relaxed">
                                            ISO certified manufacturing with international quality standards compliance
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Side - Export Destinations -->
                    <div class="p-6 lg:p-8 bg-white">
                        <div class="mb-6">
                            <h3 class="text-xl font-Michroma font-bold text-gray-900 mb-1">Export Markets</h3>
                            <p class="text-gray-600 font-Jura text-sm">Serving diverse industrial sectors globally</p>
                        </div>

                        <!-- Country List -->
                        <div class="space-y-3">
                            <!-- USA -->
                            <div class="flex items-center p-3 rounded-lg bg-gray-50/80 hover:bg-blue-50/80 transition-all duration-300 border border-transparent hover:border-blue-100/60 group">
                                <div class="w-10 h-10 flex-shrink-0 rounded-lg bg-white shadow-sm flex items-center justify-center group-hover:shadow-md transition-shadow">
                                    <img src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/4x3/us.svg" alt="USA Flag" class="w-6 h-6 rounded">
                                    </div>
                                <div class="ml-3 flex-1">
                                    <div class="font-Michroma font-semibold text-gray-900 text-sm">United States</div>
                                    </div>
                                <div class="text-[#0047AB] opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-arrow-right text-xs"></i>
                                </div>
                            </div>

                            <!-- Germany -->
                            <div class="flex items-center p-3 rounded-lg bg-gray-50/80 hover:bg-blue-50/80 transition-all duration-300 border border-transparent hover:border-blue-100/60 group">
                                <div class="w-10 h-10 flex-shrink-0 rounded-lg bg-white shadow-sm flex items-center justify-center group-hover:shadow-md transition-shadow">
                                    <img src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/4x3/de.svg" alt="Germany Flag" class="w-6 h-6 rounded">
                                    </div>
                                <div class="ml-3 flex-1">
                                    <div class="font-Michroma font-semibold text-gray-900 text-sm">Germany</div>
                                    </div>
                                <div class="text-[#0047AB] opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-arrow-right text-xs"></i>
                                </div>
                            </div>

                            <!-- Saudi Arabia -->
                            <div class="flex items-center p-3 rounded-lg bg-gray-50/80 hover:bg-blue-50/80 transition-all duration-300 border border-transparent hover:border-blue-100/60 group">
                                <div class="w-10 h-10 flex-shrink-0 rounded-lg bg-white shadow-sm flex items-center justify-center group-hover:shadow-md transition-shadow">
                                    <img src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/4x3/sa.svg" alt="Saudi Arabia Flag" class="w-6 h-6 rounded">
                                    </div>
                                <div class="ml-3 flex-1">
                                    <div class="font-Michroma font-semibold text-gray-900 text-sm">Saudi Arabia</div>
                                    </div>
                                <div class="text-[#0047AB] opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-arrow-right text-xs"></i>
                                </div>
                            </div>

                            <!-- Japan -->
                            <div class="flex items-center p-3 rounded-lg bg-gray-50/80 hover:bg-blue-50/80 transition-all duration-300 border border-transparent hover:border-blue-100/60 group">
                                <div class="w-10 h-10 flex-shrink-0 rounded-lg bg-white shadow-sm flex items-center justify-center group-hover:shadow-md transition-shadow">
                                    <img src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/4x3/jp.svg" alt="Japan Flag" class="w-6 h-6 rounded">
                                    </div>
                                <div class="ml-3 flex-1">
                                    <div class="font-Michroma font-semibold text-gray-900 text-sm">Japan</div>
                                    </div>
                                <div class="text-[#0047AB] opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-arrow-right text-xs"></i>
                            </div>
                        </div>

                            <!-- UAE -->
                            <div class="flex items-center p-3 rounded-lg bg-gray-50/80 hover:bg-blue-50/80 transition-all duration-300 border border-transparent hover:border-blue-100/60 group">
                                <div class="w-10 h-10 flex-shrink-0 rounded-lg bg-white shadow-sm flex items-center justify-center group-hover:shadow-md transition-shadow">
                                    <img src="https://raw.githubusercontent.com/lipis/flag-icons/main/flags/4x3/ae.svg" alt="UAE Flag" class="w-6 h-6 rounded">
                                    </div>
                                <div class="ml-3 flex-1">
                                    <div class="font-Michroma font-semibold text-gray-900 text-sm">UAE</div>
                                    </div>
                                <div class="text-[#0047AB] opacity-0 group-hover:opacity-100 transition-opacity">
                                    <i class="fas fa-arrow-right text-xs"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Professional CTA -->
            <div class="text-center mt-12">
                <a href="pages/contact.html" class="inline-flex items-center space-x-3 bg-[#0047AB] text-white px-6 py-3 rounded-lg hover:bg-[#003A8C] transition-all duration-300 font-Jura font-medium tracking-wide group shadow-lg hover:shadow-xl">
                    <span>Explore Export Opportunities</span>
                    <i class="fas fa-arrow-right transform group-hover:translate-x-1 transition-transform text-sm"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Clients Section -->
    <section id="clients" class="py-24 bg-gradient-to-b from-gray-50 to-white scroll-mt relative overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0">
            <img src="assets/images/hcmYN.jpg" alt="Background" class="w-full h-full object-cover opacity-[0.03]">
            <div class="absolute inset-0 bg-gradient-to-b from-white/20 via-transparent to-white/20"></div>
        </div>
        
        <!-- Background Pattern Overlay -->
        <div class="absolute inset-0">
            <div class="absolute inset-0 bg-[#0047AB]/[0.01]"></div>
            <svg class="absolute inset-0 w-full h-full opacity-[0.01]" viewBox="0 0 100 100" preserveAspectRatio="none">
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5" />
                </pattern>
                <rect width="100" height="100" fill="url(#grid)" />
            </svg>
        </div>

        <div class="container mx-auto px-4 max-w-7xl relative">
            <!-- Section Header -->
            <div class="text-center mb-20">
                <span class="text-[#D22B2B] font-Jura font-bold tracking-wider text-sm mb-3 block">OUR CLIENTS</span>
                <h2 class="text-4xl md:text-5xl font-Michroma font-bold text-gray-900 mb-6">
                    Trusted by Industry Leaders
                </h2>
                <div class="w-24 h-1 bg-[#D22B2B] rounded-full mx-auto"></div>
            </div>

            <!-- Auto-Scrolling Client Logos -->
            <div class="relative mb-16">
                <!-- Decorative Elements -->
                <div class="absolute -top-4 -left-4 w-32 h-32 bg-gradient-to-br from-[#0047AB]/10 to-transparent rounded-full blur-2xl"></div>
                <div class="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-[#D22B2B]/10 to-transparent rounded-full blur-2xl"></div>
                
                <!-- First Scrolling Row (Left to Right) -->
                <div class="overflow-hidden relative bg-gradient-to-r from-white via-blue-50/30 to-white rounded-3xl shadow-xl border border-blue-100/50 py-12 mb-10 backdrop-blur-sm client-scroll-container">
                    <!-- Gradient Overlays for smooth edges -->
                    <div class="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white to-transparent z-10 pointer-events-none"></div>
                    <div class="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white to-transparent z-10 pointer-events-none"></div>
                    
                    <div class="client-scroll-left flex items-center space-x-20 animate-scroll-left" id="clientScrollLeft">
                        <!-- First Set -->
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/RELIANCE.NS-8ac2c946.png"
                                    alt="Reliance Industries" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/LT.NS-c804dfad.png"
                                    alt="Larsen & Toubro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/TATACONSUM.NS-6da3b1a9.png"
                                    alt="TATA Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/ADANIPORTS.NS-f7134b6c.png"
                                    alt="Adani Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/M%26M.NS-ddfa8f3e.png"
                                    alt="Mahindra Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/BAJFINANCE.NS-d36b4c7e.png"
                                    alt="Bajaj Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/GODREJCP.NS-bb6a5b85.png"
                                    alt="Godrej Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/HINDUNILVR.NS-6b1c71a5.png"
                                    alt="Hindustan Unilever" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/WIPRO.NS-2f89b8c3.png"
                                    alt="Wipro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/JSWSTEEL.NS-7a79eaf9.png"
                                    alt="JSW Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <!-- Duplicate Set for Seamless Loop -->
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/RELIANCE.NS-8ac2c946.png"
                                    alt="Reliance Industries" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/LT.NS-c804dfad.png"
                                    alt="Larsen & Toubro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/TATACONSUM.NS-6da3b1a9.png"
                                    alt="TATA Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/ADANIPORTS.NS-f7134b6c.png"
                                    alt="Adani Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/M%26M.NS-ddfa8f3e.png"
                                    alt="Mahindra Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/BAJFINANCE.NS-d36b4c7e.png"
                                    alt="Bajaj Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/GODREJCP.NS-bb6a5b85.png"
                                    alt="Godrej Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/HINDUNILVR.NS-6b1c71a5.png"
                                    alt="Hindustan Unilever" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/WIPRO.NS-2f89b8c3.png"
                                    alt="Wipro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/JSWSTEEL.NS-7a79eaf9.png"
                                    alt="JSW Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Scrolling Row (Right to Left - Slower) -->
                <div class="overflow-hidden relative bg-gradient-to-r from-white via-red-50/20 to-white rounded-3xl shadow-xl border border-red-100/50 py-12 backdrop-blur-sm client-scroll-container">
                    <!-- Gradient Overlays for smooth edges -->
                    <div class="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white to-transparent z-10 pointer-events-none"></div>
                    <div class="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white to-transparent z-10 pointer-events-none"></div>
                    
                    <div class="client-scroll-right flex items-center space-x-20 animate-scroll-right" id="clientScrollRight">
                        <!-- First Set -->
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/RELIANCE.NS-8ac2c946.png"
                                    alt="Reliance Industries" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/LT.NS-c804dfad.png"
                                    alt="Larsen & Toubro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/TATACONSUM.NS-6da3b1a9.png"
                                    alt="TATA Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/ADANIPORTS.NS-f7134b6c.png"
                                    alt="Adani Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/M%26M.NS-ddfa8f3e.png"
                                    alt="Mahindra Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/BAJFINANCE.NS-d36b4c7e.png"
                                    alt="Bajaj Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/GODREJCP.NS-bb6a5b85.png"
                                    alt="Godrej Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/HINDUNILVR.NS-6b1c71a5.png"
                                    alt="Hindustan Unilever" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/WIPRO.NS-2f89b8c3.png"
                                    alt="Wipro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/JSWSTEEL.NS-7a79eaf9.png"
                                    alt="JSW Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/RELIANCE.NS-8ac2c946.png"
                                    alt="Reliance Industries" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <!-- Duplicate Set for Seamless Loop -->
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/LT.NS-c804dfad.png"
                                    alt="Larsen & Toubro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/TATACONSUM.NS-6da3b1a9.png"
                                    alt="TATA Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/ADANIPORTS.NS-f7134b6c.png"
                                    alt="Adani Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/M%26M.NS-ddfa8f3e.png"
                                    alt="Mahindra Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/BAJFINANCE.NS-d36b4c7e.png"
                                    alt="Bajaj Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/GODREJCP.NS-bb6a5b85.png"
                                    alt="Godrej Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/HINDUNILVR.NS-6b1c71a5.png"
                                    alt="Hindustan Unilever" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/WIPRO.NS-2f89b8c3.png"
                                    alt="Wipro" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                        <div class="flex items-center justify-center min-w-[200px] group">
                            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 group-hover:shadow-xl group-hover:bg-white/90 transition-all duration-500 transform group-hover:scale-105">
                                <img src="https://companieslogo.com/img/orig/JSWSTEEL.NS-7a79eaf9.png"
                                    alt="JSW Group" class="h-16 w-auto object-contain opacity-80 group-hover:opacity-100 transition-all duration-300">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trust Indicators -->
            <div class="grid md:grid-cols-3 gap-8 mt-16">
                <div
                    class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 flex items-start space-x-4 group hover:shadow-xl hover:bg-white/95 transition-all duration-300">
                    <div
                        class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center flex-shrink-0 group-hover:bg-[#0047AB]/20 transition-colors">
                        <i class="fas fa-star text-[#0047AB] text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-Michroma font-semibold text-gray-900 mb-2">Industry Leaders</h4>
                        <p class="text-gray-600 font-Jura">Trusted by top companies across various industrial sectors
                        </p>
                    </div>
                </div>

                <div
                    class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 flex items-start space-x-4 group hover:shadow-xl hover:bg-white/95 transition-all duration-300">
                    <div
                        class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center flex-shrink-0 group-hover:bg-[#0047AB]/20 transition-colors">
                        <i class="fas fa-handshake text-[#0047AB] text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-Michroma font-semibold text-gray-900 mb-2">Long-term Partners</h4>
                        <p class="text-gray-600 font-Jura">Building lasting relationships through quality and service
                        </p>
                    </div>
                </div>

                <div
                    class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50 flex items-start space-x-4 group hover:shadow-xl hover:bg-white/95 transition-all duration-300">
                    <div
                        class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center flex-shrink-0 group-hover:bg-[#0047AB]/20 transition-colors">
                        <i class="fas fa-certificate text-[#0047AB] text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-Michroma font-semibold text-gray-900 mb-2">Certified Excellence</h4>
                        <p class="text-gray-600 font-Jura">Meeting and exceeding industry quality standards</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-24 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 bg-[#0047AB]/[0.02]"></div>
        <div class="absolute right-0 top-0 w-96 h-96 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full">
        </div>
        <div
            class="absolute left-0 bottom-0 w-96 h-96 bg-gradient-to-tr from-[#D22B2B]/5 to-transparent rounded-tr-full">
        </div>

        <div class="container mx-auto px-4 max-w-7xl relative">
            <!-- Section Header -->
            <div class="text-center mb-20">
                <span class="text-[#D22B2B] font-Jura font-bold tracking-wider text-sm mb-3 block">TESTIMONIALS</span>
                <h2 class="text-4xl md:text-5xl font-Michroma font-bold text-gray-900 mb-6">
                    What Our Clients Say
                </h2>
                <div class="w-24 h-1 bg-[#D22B2B] rounded-full mx-auto"></div>
            </div>

            <!-- Desktop Testimonials Grid -->
            <div class="hidden md:grid md:grid-cols-3 gap-8 relative">
                <!-- Decorative Elements -->
                <div
                    class="absolute -left-8 top-1/2 -translate-y-1/2 w-24 h-24 border-l-2 border-t-2 border-[#0047AB]/20 rounded-tl-3xl">
                </div>
                <div
                    class="absolute -right-8 top-1/2 -translate-y-1/2 w-24 h-24 border-r-2 border-b-2 border-[#0047AB]/20 rounded-br-3xl">
                </div>

                <!-- Desktop Testimonial Cards -->
                <!-- Testimonial 1 -->
                <div
                    class="bg-white rounded-[30px] p-8 shadow-lg hover:shadow-xl transition-all duration-500 relative group">
                    <div
                        class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    </div>

                    <!-- Quote Icon -->
                    <div class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center mb-6">
                        <i class="fas fa-quote-right text-[#0047AB] text-xl"></i>
                    </div>

                    <!-- Testimonial Content -->
                    <p class="text-gray-700 font-Jura text-lg leading-relaxed mb-8">
                        "Creative Hydraulics has consistently delivered high-quality solutions that meet our exacting
                        requirements. Their attention to detail and commitment to excellence sets them apart."
                    </p>

                    <!-- Rating -->
                    <div class="flex items-center space-x-1 mb-6">
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                    </div>

                    <!-- Client Info -->
                    <div class="flex items-center">
                        <div class="w-14 h-14 rounded-xl overflow-hidden">
                            <img src="https://randomuser.me/api/portraits/men/1.jpg" alt="Rajesh Kumar"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="ml-4">
                            <h4 class="font-Michroma font-semibold text-gray-900">Rajesh Kumar</h4>
                            <p class="text-sm text-gray-500 font-Jura">Senior Engineer, Reliance Industries</p>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div
                    class="bg-white rounded-[30px] p-8 shadow-lg hover:shadow-xl transition-all duration-500 relative group md:translate-y-8">
                    <div
                        class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    </div>

                    <!-- Quote Icon -->
                    <div class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center mb-6">
                        <i class="fas fa-quote-right text-[#0047AB] text-xl"></i>
                    </div>

                    <!-- Testimonial Content -->
                    <p class="text-gray-700 font-Jura text-lg leading-relaxed mb-8">
                        "Their technical expertise and commitment to quality have made them our preferred partner. The
                        team's innovative approach to problem-solving is truly commendable."
                    </p>

                    <!-- Rating -->
                    <div class="flex items-center space-x-1 mb-6">
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                    </div>

                    <!-- Client Info -->
                    <div class="flex items-center">
                        <div class="w-14 h-14 rounded-xl overflow-hidden">
                            <img src="https://randomuser.me/api/portraits/men/2.jpg" alt="Suresh Patel"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="ml-4">
                            <h4 class="font-Michroma font-semibold text-gray-900">Suresh Patel</h4>
                            <p class="text-sm text-gray-500 font-Jura">Project Director, L&T</p>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 3 -->
                <div
                    class="bg-white rounded-[30px] p-8 shadow-lg hover:shadow-xl transition-all duration-500 relative group">
                    <div
                        class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    </div>

                    <!-- Quote Icon -->
                    <div class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center mb-6">
                        <i class="fas fa-quote-right text-[#0047AB] text-xl"></i>
                    </div>

                    <!-- Testimonial Content -->
                    <p class="text-gray-700 font-Jura text-lg leading-relaxed mb-8">
                        "Their innovative solutions have significantly improved our operational efficiency. The team's
                        responsiveness and technical expertise are truly exceptional."
                    </p>

                    <!-- Rating -->
                    <div class="flex items-center space-x-1 mb-6">
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                        <i class="fas fa-star text-[#D22B2B]"></i>
                    </div>

                    <!-- Client Info -->
                    <div class="flex items-center">
                        <div class="w-14 h-14 rounded-xl overflow-hidden">
                            <img src="https://randomuser.me/api/portraits/men/3.jpg" alt="Amit Shah"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="ml-4">
                            <h4 class="font-Michroma font-semibold text-gray-900">Amit Shah</h4>
                            <p class="text-sm text-gray-500 font-Jura">Technical Director, TATA Steel</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Testimonials Carousel -->
            <div class="md:hidden relative">
                <!-- Decorative Elements -->
                <div
                    class="absolute -left-4 top-1/2 -translate-y-1/2 w-16 h-16 border-l-2 border-t-2 border-[#0047AB]/20 rounded-tl-3xl">
                </div>
                <div
                    class="absolute -right-4 top-1/2 -translate-y-1/2 w-16 h-16 border-r-2 border-b-2 border-[#0047AB]/20 rounded-br-3xl">
                </div>

                <div class="swiper testimonialsSwiper">
                    <div class="swiper-wrapper">
                        <!-- Testimonial 1 -->
                        <div class="swiper-slide px-4">
                            <div class="bg-white rounded-[30px] p-8 shadow-lg relative group">
                                <div
                                    class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                </div>

                                <!-- Quote Icon -->
                                <div class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center mb-6">
                                    <i class="fas fa-quote-right text-[#0047AB] text-xl"></i>
                                </div>

                                <!-- Testimonial Content -->
                                <p class="text-gray-700 font-Jura text-lg leading-relaxed mb-8">
                                    "Creative Hydraulics has consistently delivered high-quality solutions that meet our
                                    exacting requirements. Their attention to detail and commitment to excellence sets
                                    them apart."
                                </p>

                                <!-- Rating -->
                                <div class="flex items-center space-x-1 mb-6">
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                </div>

                                <!-- Client Info -->
                                <div class="flex items-center">
                                    <div class="w-14 h-14 rounded-xl overflow-hidden">
                                        <img src="https://randomuser.me/api/portraits/men/1.jpg" alt="Rajesh Kumar"
                                            class="w-full h-full object-cover">
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-Michroma font-semibold text-gray-900">Rajesh Kumar</h4>
                                        <p class="text-sm text-gray-500 font-Jura">Senior Engineer, Reliance Industries
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Testimonial 2 -->
                        <div class="swiper-slide px-4">
                            <div class="bg-white rounded-[30px] p-8 shadow-lg relative group">
                                <div
                                    class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                </div>

                                <!-- Quote Icon -->
                                <div class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center mb-6">
                                    <i class="fas fa-quote-right text-[#0047AB] text-xl"></i>
                                </div>

                                <!-- Testimonial Content -->
                                <p class="text-gray-700 font-Jura text-lg leading-relaxed mb-8">
                                    "Their technical expertise and commitment to quality have made them our preferred
                                    partner. The team's innovative approach to problem-solving is truly commendable."
                                </p>

                                <!-- Rating -->
                                <div class="flex items-center space-x-1 mb-6">
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                </div>

                                <!-- Client Info -->
                                <div class="flex items-center">
                                    <div class="w-14 h-14 rounded-xl overflow-hidden">
                                        <img src="https://randomuser.me/api/portraits/men/2.jpg" alt="Suresh Patel"
                                            class="w-full h-full object-cover">
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-Michroma font-semibold text-gray-900">Suresh Patel</h4>
                                        <p class="text-sm text-gray-500 font-Jura">Project Director, L&T</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Testimonial 3 -->
                        <div class="swiper-slide px-4">
                            <div class="bg-white rounded-[30px] p-8 shadow-lg relative group">
                                <div
                                    class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#0047AB]/5 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                </div>

                                <!-- Quote Icon -->
                                <div class="w-12 h-12 rounded-xl bg-[#0047AB]/10 flex items-center justify-center mb-6">
                                    <i class="fas fa-quote-right text-[#0047AB] text-xl"></i>
                                </div>

                                <!-- Testimonial Content -->
                                <p class="text-gray-700 font-Jura text-lg leading-relaxed mb-8">
                                    "Their innovative solutions have significantly improved our operational efficiency.
                                    The team's responsiveness and technical expertise are truly exceptional."
                                </p>

                                <!-- Rating -->
                                <div class="flex items-center space-x-1 mb-6">
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                    <i class="fas fa-star text-[#D22B2B]"></i>
                                </div>

                                <!-- Client Info -->
                                <div class="flex items-center">
                                    <div class="w-14 h-14 rounded-xl overflow-hidden">
                                        <img src="https://randomuser.me/api/portraits/men/3.jpg" alt="Amit Shah"
                                            class="w-full h-full object-cover">
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-Michroma font-semibold text-gray-900">Amit Shah</h4>
                                        <p class="text-sm text-gray-500 font-Jura">Technical Director, TATA Steel</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Add Pagination -->

                    <div class="swiper-pagination !-bottom-10"></div>
                               </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="relative py-24 scroll-mt bg-gradient-to-b from-gray-50 to-white overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0">
            <div class="absolute inset-0 bg-[#0047AB]/[0.02]"></div>
            <svg class="absolute inset-0 w-full h-full opacity-[0.01]" viewBox="0 0 100 100" preserveAspectRatio="none">
                <pattern id="contact-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5" />
                </pattern>
                <rect width="100" height="100" fill="url(#contact-grid)" />
            </svg>
        </div>
        
        <!-- Decorative Elements -->
        <div class="absolute -top-4 -left-4 w-32 h-32 bg-gradient-to-br from-[#0047AB]/10 to-transparent rounded-full blur-2xl"></div>
        <div class="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-[#D22B2B]/10 to-transparent rounded-full blur-2xl"></div>
        
        <div class="container mx-auto px-4 max-w-6xl relative">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <span class="text-[#D22B2B] font-Jura font-bold tracking-wider text-sm mb-3 block">CONTACT US</span>
                <h2 class="text-4xl md:text-5xl font-Michroma font-bold text-gray-900 mb-4">
                    Get in Touch
                </h2>
                <div class="w-24 h-1 bg-[#D22B2B] rounded-full mx-auto mb-6"></div>
                <p class="text-gray-600 max-w-2xl font-Jura mx-auto text-lg">
                    Have questions? We're here to help with your hydraulic solutions and provide expert consultation.
                </p>
            </div>

            <div class="bg-white/90 backdrop-blur-sm rounded-3xl p-8 md:p-12 shadow-xl border border-white/50">
                <div class="grid lg:grid-cols-5 gap-12">
                    <!-- Contact Information -->
                    <div class="lg:col-span-2 space-y-6">
                        <div>
                            <h3 class="text-2xl font-Michroma font-semibold text-gray-900 mb-8">Contact Information</h3>
                            <div class="space-y-4 font-Jura">
                                <div class="group">
                                    <div class="flex items-start p-3 rounded-lg bg-gradient-to-r from-red-50 to-red-50/50 border border-red-100 hover:shadow-md transition-all duration-300">
                                        <div class="bg-[#D22B2B]/10 p-2 rounded-lg group-hover:bg-[#D22B2B]/20 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#D22B2B]"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h4 class="text-base font-medium text-gray-900">Phone</h4>
                                            <a href="tel:+919449024839" class="text-[#D22B2B] hover:text-[#B02020] font-medium transition-colors">+91 94490 24839</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="group">
                                    <div class="flex items-start p-3 rounded-lg bg-gradient-to-r from-red-50 to-red-50/50 border border-red-100 hover:shadow-md transition-all duration-300">
                                        <div class="bg-[#D22B2B]/10 p-2 rounded-lg group-hover:bg-[#D22B2B]/20 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#D22B2B]"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h4 class="text-base font-medium text-gray-900">Phone</h4>
                                            <a href="tel:+918971587348" class="text-[#D22B2B] hover:text-[#B02020] font-medium transition-colors">+91 ************</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="group">
                                    <div class="flex items-start p-3 rounded-lg bg-gradient-to-r from-red-50 to-red-50/50 border border-red-100 hover:shadow-md transition-all duration-300">
                                        <div class="bg-[#D22B2B]/10 p-2 rounded-lg group-hover:bg-[#D22B2B]/20 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#D22B2B]"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h4 class="text-base font-medium text-gray-900">Email</h4>
                                            <a href="mailto:<EMAIL>" class="text-[#D22B2B] hover:text-[#B02020] font-medium transition-colors"><EMAIL></a>
                                        </div>
                                    </div>
                                </div>

                                <div class="group">
                                    <div class="flex items-start p-3 rounded-lg bg-gradient-to-r from-red-50 to-red-50/50 border border-red-100 hover:shadow-md transition-all duration-300">
                                        <div class="bg-[#D22B2B]/10 p-2 rounded-lg group-hover:bg-[#D22B2B]/20 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#D22B2B]"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h4 class="text-base font-medium text-gray-900">Location</h4>
                                            <p class="text-[#D22B2B] hover:text-[#B02020] font-medium transition-colors">Sy. No. 325/2/C, Plot No.1, Khadarwadi Cross, Devendra Nagar, Udyambag, BELAGAVI-590008. (Karnataka)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Google Maps -->
                        <div class="rounded-2xl overflow-hidden h-64 border border-gray-200 shadow-lg">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3838.8527606468956!2d74.4812280812921!3d15.811715907188603!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bbf65c4e7cc4acd%3A0xdf8574b00005fff8!2sCreative%20Hydraulics!5e0!3m2!1sen!2sin!4v1751372529761!5m2!1sen!2sin" 
                                width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy">
                            </iframe>
                        </div>
                    </div>

                    <!-- Contact Form -->
                    <div class="lg:col-span-3">
                        <h3 class="text-2xl font-Michroma font-semibold text-gray-900 mb-8">Send us a Message</h3>
                        <form class="space-y-4 font-Jura">
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-800 font-medium mb-2" for="name">Full Name</label>
                                    <input type="text" id="name" name="name"
                                        class="w-full px-4 py-2.5 rounded-lg border border-gray-200 focus:border-[#D22B2B] focus:ring-2 focus:ring-[#D22B2B]/20 outline-none transition-all duration-300 bg-white/90"
                                        placeholder="John Doe">
                                </div>
                                <div>
                                    <label class="block text-gray-800 font-medium mb-2" for="email">Email Address</label>
                                    <input type="email" id="email" name="email"
                                        class="w-full px-4 py-2.5 rounded-lg border border-gray-200 focus:border-[#D22B2B] focus:ring-2 focus:ring-[#D22B2B]/20 outline-none transition-all duration-300 bg-white/90"
                                        placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div>
                                <label class="block text-gray-800 font-medium mb-2" for="subject">Subject</label>
                                <select id="subject" name="subject"
                                    class="w-full px-4 py-2.5 rounded-lg border border-gray-200 focus:border-[#D22B2B] focus:ring-2 focus:ring-[#D22B2B]/20 outline-none transition-all duration-300 bg-white/90">
                                    <option value="">Please select a subject</option>
                                    <option value="product-inquiry">Product Inquiry</option>
                                    <option value="technical-support">Technical Support</option>
                                    <option value="quotation">Request Quotation</option>
                                    <option value="partnership">Partnership Opportunity</option>
                                    <option value="general">General Inquiry</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-gray-800 font-medium mb-2" for="message">Message</label>
                                <textarea id="message" name="message" rows="4"
                                    class="w-full px-4 py-2.5 rounded-lg border border-gray-200 focus:border-[#D22B2B] focus:ring-2 focus:ring-[#D22B2B]/20 outline-none transition-all duration-300 bg-white/90 resize-none"
                                    placeholder="How can we help you?"></textarea>
                            </div>
                            
                            <button type="submit"
                                class="w-full bg-[#0047AB] text-white py-2.5 px-6 rounded-lg font-medium hover:bg-[#003A8C] transition-colors duration-300 flex items-center justify-center space-x-2">
                                <span>Send Message</span>
                                <i class="fas fa-paper-plane text-sm"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Color Strip -->
    <div class="bg-white">
        <div class="h-[29px] flex relative overflow-hidden">
            <!-- Blue Section (75%) with angled right edge -->
            <div class="w-3/4 bg-gradient-to-r from-blue-900 to-blue-700 relative" style="clip-path: polygon(0 0, 100% 0, calc(100% - 12px) 100%, 0 100%);">
            </div>
            <!-- White Gap -->
            <div class="w-0.5 bg-white"></div>
            <!-- Red Section with angled left edge -->
            <div class="flex-1 bg-gradient-to-r from-[#D22B2B] to-[#B02020] relative" style="clip-path: polygon(12px 0, 100% 0, 100% 100%, 0 100%);">
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-20 pb-10">
        <div class="container mx-auto px-4 max-w-7xl font-Jura">
            <div class="grid md:grid-cols-3 gap-12 mb-16">
                <!-- Company Info -->
                <div class="space-y-6">
                    <h3 class="text-xl font-Michroma font-bold">Creative Hydraulics</h3>
                    <p class="text-gray-400 leading-relaxed">
                        Innovative hydraulic solutions for industry leaders worldwide. Quality and reliability since 2008.
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-phone text-[#D22B2B] text-sm"></i>
                            <a href="tel:+919449024839" class="text-gray-400 hover:text-white transition-colors">
                                +91 ************
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-phone text-[#D22B2B] text-sm"></i>
                            <a href="tel:+918971587348" class="text-gray-400 hover:text-white transition-colors">
                                +91 ************
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-envelope text-[#D22B2B] text-sm"></i>
                            <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white transition-colors">
                                <EMAIL>
                            </a>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-map-marker-alt text-[#D22B2B] text-sm"></i>
                            <span class="text-gray-400">
                                Sy. No. 325/2/C, Plot No.1, Khadarwadi Cross, Devendra Nagar, Udyambag, BELAGAVI-590008. (Karnataka)
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-display font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-4">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="pages/about.html" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#certifications" class="text-gray-400 hover:text-white transition-colors">Certifications</a></li>
                        <li><a href="pages/infrastructure.html" class="text-gray-400 hover:text-white transition-colors">Infrastructure</a></li>
                        <li><a href="pages/contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Products -->
                <div>
                    <h4 class="text-lg font-display font-semibold mb-6">Our Products</h4>
                    <ul class="space-y-4">
                        <li><a href="pages/products.html" class="text-gray-400 hover:text-white transition-colors">View All Products</a></li>
                        <li><a href="pages/manual.html" class="text-gray-400 hover:text-white transition-colors">Manual Systems</a></li>
                        <li><a href="pages/semi.html" class="text-gray-400 hover:text-white transition-colors">Semi-Automatic Systems</a></li>
                        <li><a href="pages/auto.html" class="text-gray-400 hover:text-white transition-colors">Fully Automatic Systems</a></li>
                        <li><a href="pages/hts.html" class="text-gray-400 hover:text-white transition-colors">Horizontal Test Stand</a></li>
                        <li><a href="pages/vts.html" class="text-gray-400 hover:text-white transition-colors">Vertical Test Stand</a></li>
                        <li><a href="pages/mshts.html" class="text-gray-400 hover:text-white transition-colors">Multi-Station Test Stand</a></li>
                        <li><a href="pages/fsts.html" class="text-gray-400 hover:text-white transition-colors">Fire Safe Test Stand</a></li>
                        <li><a href="pages/cts.html" class="text-gray-400 hover:text-white transition-colors">Cryogenic Test Stand</a></li>
                        <li><a href="pages/fet.html" class="text-gray-400 hover:text-white transition-colors">Fugitive Emission Test</a></li>
                        <li><a href="pages/hp.html" class="text-gray-400 hover:text-white transition-colors">Hydraulic Presses</a></li>
                        <li><a href="pages/spm.html" class="text-gray-400 hover:text-white transition-colors">Special Purpose Machine</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <p class="text-gray-400 text-sm">
                        © 2024 Creative Hydraulics. All rights reserved.
                    </p>
                    <div class="flex space-x-6">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="LinkedIn">
                            <i class="fab fa-linkedin text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Twitter">
                            <i class="fab fa-twitter text-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors" aria-label="Facebook">
                            <i class="fab fa-facebook text-lg"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Add Swiper JS before closing body -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        // Mobile Products Dropdown Toggle
        function toggleMobileDropdown() {
            const dropdown = document.getElementById('mobileProductsDropdown');
            const icon = document.getElementById('mobileDropdownIcon');
            
            dropdown.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

            

        document.addEventListener('DOMContentLoaded', function() {
            // Mobile Menu Functionality
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuContent = document.getElementById('mobile-menu-content');
            const mobileMenuBackdrop = document.getElementById('mobile-menu-backdrop');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            function toggleMobileMenu() {
                mobileMenu.classList.toggle('hidden');
                setTimeout(() => {
                    mobileMenuBackdrop.classList.toggle('opacity-0');
                    mobileMenuContent.classList.toggle('translate-x-full');
                }, 50);
            }

            mobileMenuButton.addEventListener('click', toggleMobileMenu);
            mobileMenuClose.addEventListener('click', toggleMobileMenu);
            mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

            // Close mobile menu when clicking on a link
            const mobileMenuLinks = mobileMenu.querySelectorAll('a');
            mobileMenuLinks.forEach(link => {
                link.addEventListener('click', toggleMobileMenu);
            });

            // Initialize Products Swiper
            const productsSwiper = new Swiper('.productsSwiper', {
                slidesPerView: 1,
                spaceBetween: 20,
                loop: true,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                centeredSlides: true,
                breakpoints: {
                    480: {
                        slidesPerView: 1,
                        spaceBetween: 20,
                    },
                    640: {
                        slidesPerView: 1,
                        spaceBetween: 24,
                    }
                }
            });

            // Initialize Testimonials Swiper
            const testimonialsSwiper = new Swiper('.testimonialsSwiper', {
                slidesPerView: 1,
                spaceBetween: 20,
                loop: true,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                speed: 600,
            });

            // Hero Video/Image Carousel
            const slides = document.querySelectorAll('#hero-carousel .hero-slide');
            let current = 0;
            function showSlide(idx) {
                slides.forEach((slide, i) => {
                    slide.style.opacity = (i === idx) ? '1' : '0';
                    slide.style.zIndex = (i === idx) ? '2' : '1';
                });
            }
            function nextSlide() {
                current = (current + 1) % slides.length;
                showSlide(current);
            }
            showSlide(current);
            setInterval(nextSlide, 7000);

            // Client Scrolling Touch/Drag Functionality
            function initializeClientScrollTouch() {
                const scrollContainers = document.querySelectorAll('.client-scroll-container');
                
                scrollContainers.forEach(container => {
                    const scrollElement = container.querySelector('.client-scroll-left, .client-scroll-right');
                    if (!scrollElement) return;
                    
                    let isDragging = false;
                    let startX = 0;
                    let scrollLeft = 0;
                    let autoScrollTimeout;
                    
                    // Mouse events
                    container.addEventListener('mousedown', handleStart);
                    container.addEventListener('mousemove', handleMove);
                    container.addEventListener('mouseup', handleEnd);
                    container.addEventListener('mouseleave', handleEnd);
                    
                    // Touch events
                    container.addEventListener('touchstart', handleStart, { passive: false });
                    container.addEventListener('touchmove', handleMove, { passive: false });
                    container.addEventListener('touchend', handleEnd);
                    
                    function handleStart(e) {
                        isDragging = true;
                        startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                        scrollLeft = container.scrollLeft;
                        
                        // Pause animation
                        scrollElement.classList.add('client-scroll-paused');
                        container.style.cursor = 'grabbing';
                        
                        // Clear auto-resume timeout
                        if (autoScrollTimeout) {
                            clearTimeout(autoScrollTimeout);
                        }
                    }
                    
                    function handleMove(e) {
                        if (!isDragging) return;
                        
                        e.preventDefault();
                        const x = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
                        const walk = (x - startX) * 2; // Scroll speed multiplier
                        container.scrollLeft = scrollLeft - walk;
                    }
                    
                    function handleEnd() {
                        if (!isDragging) return;
                        
                        isDragging = false;
                        container.style.cursor = 'grab';
                        
                        // Resume animation after 2 seconds of inactivity
                        autoScrollTimeout = setTimeout(() => {
                            scrollElement.classList.remove('client-scroll-paused');
                        }, 2000);
                    }
                    
                    // Prevent default drag behavior on images
                    container.addEventListener('dragstart', (e) => e.preventDefault());
                });
            }
            
            // Initialize client scroll touch functionality
            initializeClientScrollTouch();
        });
    </script>
</body>

</html>