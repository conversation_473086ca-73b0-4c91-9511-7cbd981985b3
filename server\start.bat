@echo off
echo ========================================
echo Starting Creative Hydraulics Admin Server
echo ========================================
echo.

if not exist node_modules (
    echo Installing dependencies...
    npm install
    echo.
)

if not exist .env (
    echo Creating environment file...
    copy .env.example .env
    echo Please edit .env with your Firebase credentials before running again.
    pause
    exit /b 1
)

echo Starting server...
echo.
echo Admin Panel: http://localhost:3000/admin
echo API Health: http://localhost:3000/api/health
echo.

npm start
