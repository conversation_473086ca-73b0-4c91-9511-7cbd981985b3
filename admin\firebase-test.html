<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Test - Creative Hydraulics</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #0047AB;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #003A8C;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Firebase Connection Test</h1>
        <p>This page tests the Firebase configuration for Creative Hydraulics admin panel.</p>
        
        <div id="status" class="status info">Initializing Firebase...</div>
        
        <div>
            <button id="testDatabase">Test Database</button>
            <button id="testStorage">Test Storage</button>
            <button id="clearLog">Clear Log</button>
        </div>
        
        <h3>Console Log:</h3>
        <div id="log"></div>
        
        <input type="file" id="testFile" accept="image/*" style="margin: 20px 0;">
    </div>

    <script type="module">
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        try {
            addLog('Loading Firebase modules...');
            
            const { initializeApp } = await import("https://www.gstatic.com/firebasejs/11.10.0/firebase-app.js");
            const { getDatabase, ref, push, set, onValue, serverTimestamp } = await import("https://www.gstatic.com/firebasejs/11.10.0/firebase-database.js");
            const { getStorage, ref: storageRef, uploadBytes, getDownloadURL } = await import("https://www.gstatic.com/firebasejs/11.10.0/firebase-storage.js");
            
            addLog('Firebase modules loaded successfully');
            
            const firebaseConfig = {
                apiKey: "AIzaSyC8aAJOv3r43YscYB9A9jZFadRn_1GRG7E",
                authDomain: "chwk-edca8.firebaseapp.com",
                projectId: "chwk-edca8",
                storageBucket: "chwk-edca8.firebasestorage.app",
                messagingSenderId: "456605094673",
                appId: "1:456605094673:web:609879171843944d1db2b0",
                databaseURL: "https://chwk-edca8-default-rtdb.firebaseio.com/"
            };
            
            addLog('Initializing Firebase...');
            const app = initializeApp(firebaseConfig);
            const database = getDatabase(app);
            const storage = getStorage(app);
            
            addLog('Firebase initialized successfully');
            updateStatus('Firebase connected successfully!', 'success');
            
            // Test Database
            document.getElementById('testDatabase').addEventListener('click', async () => {
                try {
                    addLog('Testing database write...');
                    const testRef = ref(database, 'test');
                    await set(testRef, {
                        timestamp: Date.now(),
                        message: 'Database test successful',
                        serverTime: serverTimestamp()
                    });
                    addLog('Database write successful!');
                    
                    addLog('Testing database read...');
                    onValue(testRef, (snapshot) => {
                        const data = snapshot.val();
                        addLog('Database read successful: ' + JSON.stringify(data));
                    });
                    
                } catch (error) {
                    addLog('Database test failed: ' + error.message);
                }
            });
            
            // Test Storage
            document.getElementById('testStorage').addEventListener('click', async () => {
                const fileInput = document.getElementById('testFile');
                if (!fileInput.files[0]) {
                    alert('Please select a file first');
                    return;
                }
                
                try {
                    addLog('Testing storage upload...');
                    const file = fileInput.files[0];
                    const fileName = `test_${Date.now()}_${file.name}`;
                    const imageRef = storageRef(storage, `test/${fileName}`);
                    
                    const uploadResult = await uploadBytes(imageRef, file);
                    addLog('File uploaded successfully');
                    
                    const downloadURL = await getDownloadURL(uploadResult.ref);
                    addLog('Download URL obtained: ' + downloadURL);
                    
                } catch (error) {
                    addLog('Storage test failed: ' + error.message);
                }
            });
            
            // Clear log
            document.getElementById('clearLog').addEventListener('click', () => {
                log.textContent = '';
            });
            
        } catch (error) {
            addLog('Error initializing Firebase: ' + error.message);
            updateStatus('Firebase initialization failed: ' + error.message, 'error');
        }
    </script>
</body>
</html>
