<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Admin - Creative Hydraulics</title>
    <link rel="icon" type="image/png" href="/assets/logos/logo.png">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Jura:wght@300..700&family=Michroma&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'Jura': ['Jura', 'sans-serif'],
                        'Michroma': ['Michroma', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    
    <style>
        .font-Michroma { font-family: 'Michroma', sans-serif; }
        .font-Jura { font-family: 'Jura', sans-serif; }
        
        .upload-area {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #0047AB;
            background-color: #f8fafc;
        }
        
        .upload-area.dragover {
            border-color: #0047AB;
            background-color: #eff6ff;
        }
    </style>
</head>

<body class="font-body bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/assets/logos/logo.png" alt="Creative Hydraulics Logo" class="w-12 h-12 object-contain" />
                    <div>
                        <h1 class="text-xl font-Michroma font-bold text-gray-900">Creative Hydraulics</h1>
                        <p class="text-sm font-Jura text-gray-600">Server Admin Panel</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div id="serverStatus" class="flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-circle text-xs mr-2"></i>
                        <span class="text-sm font-Jura">Server Online</span>
                    </div>
                    <a href="/pages/products.html" target="_blank" class="bg-[#0047AB] hover:bg-[#003A8C] text-white px-6 py-2 rounded-lg font-Jura font-medium transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>View Products
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Page Title -->
            <div class="text-center mb-8">
                <h2 class="text-3xl font-Michroma font-bold text-gray-900 mb-2">Product Management</h2>
                <p class="text-gray-600 font-Jura">Server-powered admin panel with Node.js backend</p>
                <div class="w-20 h-0.5 bg-[#D22B2B] rounded-full mx-auto mt-4"></div>
            </div>

            <!-- Success/Error Messages -->
            <div id="successMessage" class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span id="successText">Product added successfully!</span>
                </div>
            </div>

            <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span id="errorText">An error occurred</span>
                </div>
            </div>

            <!-- Add Product Form -->
            <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
                <h3 class="text-xl font-Michroma font-bold text-gray-900 mb-6">Add New Product</h3>
                
                <form id="productForm" class="space-y-6">
                    <!-- Product Name -->
                    <div>
                        <label for="productName" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="productName" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura"
                               placeholder="Enter product name">
                    </div>

                    <!-- Product Description -->
                    <div>
                        <label for="productDescription" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Description <span class="text-red-500">*</span>
                        </label>
                        <textarea id="productDescription" required rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura resize-none"
                                  placeholder="Enter detailed product description"></textarea>
                    </div>

                    <!-- Product Image -->
                    <div>
                        <label class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                            Product Image <span class="text-red-500">*</span>
                        </label>
                        <div id="uploadArea" class="upload-area p-8 rounded-lg text-center cursor-pointer">
                            <div id="uploadContent">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600 font-Jura mb-2">Click to upload or drag and drop</p>
                                <p class="text-sm text-gray-500 font-Jura">PNG, JPG, JPEG, WebP up to 10MB</p>
                            </div>
                            <div id="imagePreview" class="hidden">
                                <img id="previewImg" class="max-w-48 max-h-48 object-cover rounded-lg mx-auto mb-4" alt="Preview">
                                <p class="text-sm text-gray-600 font-Jura">Click to change image</p>
                            </div>
                        </div>
                        <input type="file" id="imageInput" accept="image/*" class="hidden">
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4">
                        <button type="button" id="resetBtn" 
                                class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-Jura font-medium transition-colors">
                            Reset Form
                        </button>
                        <button type="submit" id="submitBtn"
                                class="px-6 py-3 bg-[#0047AB] hover:bg-[#003A8C] text-white rounded-lg font-Jura font-medium transition-colors">
                            <span id="submitText">Add Product</span>
                            <span id="loadingSpinner" class="hidden">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Adding...
                            </span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Recent Products -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-Michroma font-bold text-gray-900">Recent Products</h3>
                    <button id="refreshBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-Jura font-medium transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
                <div id="recentProducts" class="space-y-4">
                    <div class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-3xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500 font-Jura">Loading products...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Admin Panel JavaScript for Server-based Admin
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Server Admin Panel loaded');

            // DOM Elements
            const productForm = document.getElementById('productForm');
            const uploadArea = document.getElementById('uploadArea');
            const imageInput = document.getElementById('imageInput');
            const uploadContent = document.getElementById('uploadContent');
            const imagePreview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const successText = document.getElementById('successText');
            const errorText = document.getElementById('errorText');
            const resetBtn = document.getElementById('resetBtn');
            const recentProducts = document.getElementById('recentProducts');
            const refreshBtn = document.getElementById('refreshBtn');
            const serverStatus = document.getElementById('serverStatus');

            let selectedFile = null;

            // Check server status
            async function checkServerStatus() {
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();

                    if (data.status === 'OK') {
                        serverStatus.className = 'flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800';
                        serverStatus.innerHTML = '<i class="fas fa-circle text-xs mr-2"></i><span class="text-sm font-Jura">Server Online</span>';
                    } else {
                        throw new Error('Server not responding');
                    }
                } catch (error) {
                    serverStatus.className = 'flex items-center px-3 py-1 rounded-full bg-red-100 text-red-800';
                    serverStatus.innerHTML = '<i class="fas fa-circle text-xs mr-2"></i><span class="text-sm font-Jura">Server Offline</span>';
                }
            }

            // Show message
            function showMessage(type, message) {
                if (type === 'success') {
                    successText.textContent = message;
                    successMessage.classList.remove('hidden');
                    errorMessage.classList.add('hidden');
                    setTimeout(() => successMessage.classList.add('hidden'), 5000);
                } else {
                    errorText.textContent = message;
                    errorMessage.classList.remove('hidden');
                    successMessage.classList.add('hidden');
                    setTimeout(() => errorMessage.classList.add('hidden'), 5000);
                }
            }

            // Image upload handling
            uploadArea.addEventListener('click', () => imageInput.click());

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) handleFileSelect(files[0]);
            });

            imageInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) handleFileSelect(e.target.files[0]);
            });

            function handleFileSelect(file) {
                if (!file.type.startsWith('image/')) {
                    showMessage('error', 'Please select an image file');
                    return;
                }
                if (file.size > 10 * 1024 * 1024) {
                    showMessage('error', 'File size must be less than 10MB');
                    return;
                }

                selectedFile = file;
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImg.src = e.target.result;
                    uploadContent.classList.add('hidden');
                    imagePreview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }

            // Form submission
            productForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                const productName = document.getElementById('productName').value.trim();
                const productDescription = document.getElementById('productDescription').value.trim();

                if (!productName || !productDescription || !selectedFile) {
                    showMessage('error', 'Please fill in all fields and select an image');
                    return;
                }

                // Show loading
                submitBtn.disabled = true;
                submitText.classList.add('hidden');
                loadingSpinner.classList.remove('hidden');

                try {
                    const formData = new FormData();
                    formData.append('name', productName);
                    formData.append('description', productDescription);
                    formData.append('image', selectedFile);

                    const response = await fetch('/api/products', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        showMessage('success', 'Product added successfully!');
                        resetForm();
                        loadRecentProducts();
                    } else {
                        throw new Error(result.message || 'Failed to add product');
                    }

                } catch (error) {
                    console.error('Error:', error);
                    showMessage('error', 'Error adding product: ' + error.message);
                } finally {
                    submitBtn.disabled = false;
                    submitText.classList.remove('hidden');
                    loadingSpinner.classList.add('hidden');
                }
            });

            // Reset form
            resetBtn.addEventListener('click', resetForm);

            function resetForm() {
                productForm.reset();
                selectedFile = null;
                uploadContent.classList.remove('hidden');
                imagePreview.classList.add('hidden');
            }

            // Load recent products
            async function loadRecentProducts() {
                try {
                    const response = await fetch('/api/products');
                    const result = await response.json();

                    if (result.success) {
                        const products = result.products.slice(0, 5); // Show last 5

                        if (products.length === 0) {
                            recentProducts.innerHTML = '<p class="text-gray-500 font-Jura text-center py-8">No products added yet</p>';
                            return;
                        }

                        const html = products.map(product => `
                            <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <img src="${product.imageUrl}" alt="${product.name}" class="w-16 h-16 object-cover rounded-lg shadow-sm">
                                <div class="flex-1">
                                    <h4 class="font-Jura font-semibold text-gray-900">${product.name}</h4>
                                    <p class="text-sm text-gray-600 font-Jura">${product.description.substring(0, 100)}${product.description.length > 100 ? '...' : ''}</p>
                                    <p class="text-xs text-gray-500 font-Jura mt-1">
                                        Added: ${new Date(product.createdAt).toLocaleDateString()}
                                    </p>
                                </div>
                                <button onclick="deleteProduct('${product.id}')" class="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `).join('');

                        recentProducts.innerHTML = html;
                    } else {
                        throw new Error(result.message || 'Failed to load products');
                    }
                } catch (error) {
                    console.error('Error loading products:', error);
                    recentProducts.innerHTML = '<p class="text-red-500 font-Jura text-center py-8">Error loading products</p>';
                }
            }

            // Delete product
            window.deleteProduct = async function(productId) {
                if (!confirm('Are you sure you want to delete this product?')) {
                    return;
                }

                try {
                    const response = await fetch(`/api/products/${productId}`, {
                        method: 'DELETE'
                    });

                    const result = await response.json();

                    if (result.success) {
                        showMessage('success', 'Product deleted successfully!');
                        loadRecentProducts();
                    } else {
                        throw new Error(result.message || 'Failed to delete product');
                    }
                } catch (error) {
                    console.error('Error deleting product:', error);
                    showMessage('error', 'Error deleting product: ' + error.message);
                }
            };

            // Refresh products
            refreshBtn.addEventListener('click', loadRecentProducts);

            // Initialize
            checkServerStatus();
            loadRecentProducts();

            // Check server status periodically
            setInterval(checkServerStatus, 30000); // Every 30 seconds
        });
    </script>
</body>
</html>
