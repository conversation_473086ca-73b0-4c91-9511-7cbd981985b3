<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hybrid Admin - Creative Hydraulics</title>
    <link rel="icon" type="image/png" href="/assets/logos/logo.png">
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Jura:wght@300..700&family=Michroma&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'Jura': ['Jura', 'sans-serif'],
                        'Michroma': ['Michroma', 'sans-serif'],
                        'body': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    
    <style>
        .font-Michroma { font-family: 'Michroma', sans-serif; }
        .font-Jura { font-family: 'Jura', sans-serif; }
        
        .upload-area {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #0047AB;
            background-color: #f8fafc;
        }
        
        .upload-area.dragover {
            border-color: #0047AB;
            background-color: #eff6ff;
        }

        .product-card {
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>

<body class="font-body bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-lg border-b border-gray-200">
        <div class="container mx-auto px-4 sm:px-6 py-4">
            <div class="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
                <div class="flex items-center space-x-3">
                    <img src="/assets/logos/logo.png" alt="Creative Hydraulics Logo" class="w-10 h-10 sm:w-12 sm:h-12 object-contain" />
                    <div class="text-center sm:text-left">
                        <h1 class="text-lg sm:text-xl font-Michroma font-bold text-gray-900">Creative Hydraulics</h1>
                        <p class="text-xs sm:text-sm font-Jura text-gray-600">Hybrid Admin Panel</p>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-3">
                    <div id="firebaseStatus" class="flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800">
                        <i class="fas fa-circle text-xs mr-2"></i>
                        <span class="text-xs sm:text-sm font-Jura">Connecting...</span>
                    </div>
                    <a href="/pages/products.html" target="_blank" class="bg-[#0047AB] hover:bg-[#003A8C] text-white px-4 sm:px-6 py-2 rounded-lg font-Jura font-medium transition-colors text-sm">
                        <i class="fas fa-external-link-alt mr-2"></i>View Products
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Page Title -->
            <div class="text-center mb-8">
                <h2 class="text-2xl sm:text-3xl font-Michroma font-bold text-gray-900 mb-2">Product Management</h2>
                <p class="text-gray-600 font-Jura text-sm sm:text-base">Hybrid server & client-side Firebase integration</p>
                <div class="w-20 h-0.5 bg-[#D22B2B] rounded-full mx-auto mt-4"></div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
                <!-- Add Product Form -->
                <div class="lg:col-span-2">
                    <!-- Success/Error Messages -->
                    <div id="successMessage" class="hidden bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span id="successText">Product added successfully!</span>
                        </div>
                    </div>

                    <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <span id="errorText">An error occurred</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-lg p-6 sm:p-8">
                        <h3 class="text-lg sm:text-xl font-Michroma font-bold text-gray-900 mb-6">Add New Product</h3>
                        
                        <form id="productForm" class="space-y-6">
                            <!-- Product Name -->
                            <div>
                                <label for="productName" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                                    Product Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="productName" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura text-sm sm:text-base"
                                       placeholder="Enter product name">
                            </div>

                            <!-- Product Description -->
                            <div>
                                <label for="productDescription" class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                                    Product Description <span class="text-red-500">*</span>
                                </label>
                                <textarea id="productDescription" required rows="4"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-Jura resize-none text-sm sm:text-base"
                                          placeholder="Enter detailed product description"></textarea>
                            </div>

                            <!-- Product Image -->
                            <div>
                                <label class="block text-sm font-Jura font-semibold text-gray-700 mb-2">
                                    Product Image <span class="text-red-500">*</span>
                                </label>
                                <div id="uploadArea" class="upload-area p-6 sm:p-8 rounded-lg text-center cursor-pointer">
                                    <div id="uploadContent">
                                        <i class="fas fa-cloud-upload-alt text-3xl sm:text-4xl text-gray-400 mb-4"></i>
                                        <p class="text-gray-600 font-Jura mb-2 text-sm sm:text-base">Click to upload or drag and drop</p>
                                        <p class="text-xs sm:text-sm text-gray-500 font-Jura">PNG, JPG, JPEG, WebP up to 10MB</p>
                                    </div>
                                    <div id="imagePreview" class="hidden">
                                        <img id="previewImg" class="max-w-full max-h-48 object-cover rounded-lg mx-auto mb-4" alt="Preview">
                                        <p class="text-xs sm:text-sm text-gray-600 font-Jura">Click to change image</p>
                                    </div>
                                </div>
                                <input type="file" id="imageInput" accept="image/*" class="hidden">
                            </div>

                            <!-- Submit Button -->
                            <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4">
                                <button type="button" id="resetBtn" 
                                        class="w-full sm:w-auto px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-Jura font-medium transition-colors text-sm sm:text-base">
                                    Reset Form
                                </button>
                                <button type="submit" id="submitBtn"
                                        class="w-full sm:w-auto px-6 py-3 bg-[#0047AB] hover:bg-[#003A8C] text-white rounded-lg font-Jura font-medium transition-colors text-sm sm:text-base">
                                    <span id="submitText">Add Product</span>
                                    <span id="loadingSpinner" class="hidden">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Adding...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Recent Products Sidebar -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl shadow-lg p-6 sticky top-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-Michroma font-bold text-gray-900">Recent Products</h3>
                            <button id="refreshBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded-lg font-Jura font-medium transition-colors text-sm">
                                <i class="fas fa-sync-alt mr-1"></i>Refresh
                            </button>
                        </div>
                        <div id="recentProducts" class="space-y-4 max-h-96 overflow-y-auto">
                            <div class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                                <p class="text-gray-500 font-Jura text-sm">Loading products...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Preview -->
            <div class="mt-12">
                <div class="text-center mb-8">
                    <h3 class="text-xl sm:text-2xl font-Michroma font-bold text-gray-900 mb-2">Live Products Preview</h3>
                    <p class="text-gray-600 font-Jura text-sm sm:text-base">See how products appear on the main website</p>
                </div>
                
                <div id="productsPreview" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-box-open text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500 font-Jura">No products to preview yet</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC8aAJOv3r43YscYB9A9jZFadRn_1GRG7E",
            authDomain: "chwk-edca8.firebaseapp.com",
            projectId: "chwk-edca8",
            storageBucket: "chwk-edca8.firebasestorage.app",
            messagingSenderId: "456605094673",
            appId: "1:456605094673:web:609879171843944d1db2b0",
            databaseURL: "https://chwk-edca8-default-rtdb.firebaseio.com/"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const database = firebase.database();
        const storage = firebase.storage();

        console.log('Firebase initialized for hybrid admin');

        // Hybrid Admin Panel JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Hybrid Admin Panel loaded');

            // DOM Elements
            const productForm = document.getElementById('productForm');
            const uploadArea = document.getElementById('uploadArea');
            const imageInput = document.getElementById('imageInput');
            const uploadContent = document.getElementById('uploadContent');
            const imagePreview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const successText = document.getElementById('successText');
            const errorText = document.getElementById('errorText');
            const resetBtn = document.getElementById('resetBtn');
            const recentProducts = document.getElementById('recentProducts');
            const refreshBtn = document.getElementById('refreshBtn');
            const firebaseStatus = document.getElementById('firebaseStatus');
            const productsPreview = document.getElementById('productsPreview');

            let selectedFile = null;

            // Update Firebase status
            function updateFirebaseStatus(status, message) {
                const statusClasses = {
                    connected: 'bg-green-100 text-green-800',
                    error: 'bg-red-100 text-red-800',
                    connecting: 'bg-yellow-100 text-yellow-800'
                };

                firebaseStatus.className = `flex items-center px-3 py-1 rounded-full ${statusClasses[status]}`;
                firebaseStatus.innerHTML = `<i class="fas fa-circle text-xs mr-2"></i><span class="text-xs sm:text-sm font-Jura">${message}</span>`;
            }

            // Show message
            function showMessage(type, message) {
                if (type === 'success') {
                    successText.textContent = message;
                    successMessage.classList.remove('hidden');
                    errorMessage.classList.add('hidden');
                    setTimeout(() => successMessage.classList.add('hidden'), 5000);
                } else {
                    errorText.textContent = message;
                    errorMessage.classList.remove('hidden');
                    successMessage.classList.add('hidden');
                    setTimeout(() => errorMessage.classList.add('hidden'), 5000);
                }
            }

            // Image upload handling
            uploadArea.addEventListener('click', () => imageInput.click());

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) handleFileSelect(files[0]);
            });

            imageInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) handleFileSelect(e.target.files[0]);
            });

            function handleFileSelect(file) {
                if (!file.type.startsWith('image/')) {
                    showMessage('error', 'Please select an image file');
                    return;
                }
                if (file.size > 10 * 1024 * 1024) {
                    showMessage('error', 'File size must be less than 10MB');
                    return;
                }

                selectedFile = file;
                const reader = new FileReader();
                reader.onload = (e) => {
                    previewImg.src = e.target.result;
                    uploadContent.classList.add('hidden');
                    imagePreview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }

            // Form submission - Hybrid approach
            productForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                const productName = document.getElementById('productName').value.trim();
                const productDescription = document.getElementById('productDescription').value.trim();

                if (!productName || !productDescription || !selectedFile) {
                    showMessage('error', 'Please fill in all fields and select an image');
                    return;
                }

                // Show loading
                submitBtn.disabled = true;
                submitText.classList.add('hidden');
                loadingSpinner.classList.remove('hidden');

                try {
                    // Try server-side upload first
                    const formData = new FormData();
                    formData.append('name', productName);
                    formData.append('description', productDescription);
                    formData.append('image', selectedFile);

                    const response = await fetch('/api/products', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success && !result.product.needsUpload) {
                        // Server successfully handled everything
                        showMessage('success', 'Product added successfully via server!');
                        resetForm();
                        loadRecentProducts();
                        loadProductsPreview();
                    } else if (result.success && result.product.needsUpload) {
                        // Server returned data for client-side upload
                        console.log('Handling client-side Firebase upload...');
                        await handleClientSideUpload(result.product);
                    } else {
                        throw new Error(result.message || 'Server upload failed');
                    }

                } catch (error) {
                    console.error('Server upload failed, trying client-side:', error);

                    // Fallback to pure client-side Firebase
                    try {
                        await handleClientSideUpload({
                            name: productName,
                            description: productDescription,
                            needsUpload: true
                        });
                    } catch (clientError) {
                        console.error('Client-side upload also failed:', clientError);
                        showMessage('error', 'Failed to upload product: ' + clientError.message);
                    }
                } finally {
                    submitBtn.disabled = false;
                    submitText.classList.remove('hidden');
                    loadingSpinner.classList.add('hidden');
                }
            });

            // Client-side Firebase upload
            async function handleClientSideUpload(productData) {
                console.log('Starting client-side Firebase upload...');

                // Upload image to Firebase Storage
                const timestamp = Date.now();
                const fileName = `${timestamp}_${selectedFile.name.replace(/[^a-zA-Z0-9.]/g, '_')}`;
                const storageRef = storage.ref(`products/${fileName}`);

                const uploadTask = await storageRef.put(selectedFile);
                const imageUrl = await uploadTask.ref.getDownloadURL();

                console.log('Image uploaded to Firebase Storage:', imageUrl);

                // Save product data to Firebase Database
                const finalProductData = {
                    name: productData.name,
                    description: productData.description,
                    imageUrl: imageUrl,
                    timestamp: firebase.database.ServerValue.TIMESTAMP,
                    createdAt: new Date().toISOString()
                };

                await database.ref('customProducts').push(finalProductData);
                console.log('Product saved to Firebase Database');

                showMessage('success', 'Product added successfully via client!');
                resetForm();
                loadRecentProducts();
                loadProductsPreview();
            }

            // Reset form
            resetBtn.addEventListener('click', resetForm);

            function resetForm() {
                productForm.reset();
                selectedFile = null;
                uploadContent.classList.remove('hidden');
                imagePreview.classList.add('hidden');
            }

            // Load recent products
            async function loadRecentProducts() {
                try {
                    database.ref('customProducts').orderByChild('timestamp').limitToLast(5).on('value', (snapshot) => {
                        const products = snapshot.val();

                        if (!products) {
                            recentProducts.innerHTML = '<p class="text-gray-500 font-Jura text-center py-8 text-sm">No products added yet</p>';
                            return;
                        }

                        const productsArray = Object.entries(products).reverse();
                        const html = productsArray.map(([id, product]) => `
                            <div class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <img src="${product.imageUrl}" alt="${product.name}" class="w-12 h-12 object-cover rounded-lg shadow-sm flex-shrink-0">
                                <div class="flex-1 min-w-0">
                                    <h4 class="font-Jura font-semibold text-gray-900 text-sm truncate">${product.name}</h4>
                                    <p class="text-xs text-gray-600 font-Jura truncate">${product.description}</p>
                                </div>
                                <button onclick="deleteProduct('${id}')" class="text-red-500 hover:text-red-700 p-1 rounded transition-colors flex-shrink-0">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                            </div>
                        `).join('');

                        recentProducts.innerHTML = html;
                    });
                } catch (error) {
                    console.error('Error loading recent products:', error);
                    recentProducts.innerHTML = '<p class="text-red-500 font-Jura text-center py-8 text-sm">Error loading products</p>';
                }
            }

            // Load products preview
            function loadProductsPreview() {
                database.ref('customProducts').on('value', (snapshot) => {
                    const products = snapshot.val();

                    if (!products) {
                        productsPreview.innerHTML = `
                            <div class="col-span-full text-center py-12">
                                <i class="fas fa-box-open text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-500 font-Jura">No products to preview yet</p>
                            </div>
                        `;
                        return;
                    }

                    const productsArray = Object.entries(products).sort((a, b) => (b[1].timestamp || 0) - (a[1].timestamp || 0));
                    const html = productsArray.map(([id, product]) => `
                        <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
                            <div class="relative h-48 overflow-hidden">
                                <img src="${product.imageUrl}"
                                     alt="${product.name}"
                                     class="w-full h-full object-cover"
                                     onerror="this.src='https://via.placeholder.com/400x300/f3f4f6/9ca3af?text=Image+Not+Found'">
                                <div class="absolute top-3 right-3">
                                    <span class="px-2 py-1 bg-[#D22B2B] text-white text-xs font-Jura font-medium rounded-full">Custom</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-base font-Michroma font-bold text-gray-900 mb-2 line-clamp-1">${product.name}</h3>
                                <p class="text-gray-600 font-Jura text-sm mb-3 line-clamp-2">${product.description}</p>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Custom Built</span>
                                    <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs font-Jura font-medium border border-gray-200 rounded">Tailored</span>
                                </div>
                                <a href="/pages/contact.html" class="inline-flex items-center text-[#0047AB] hover:text-[#D22B2B] font-Jura font-medium transition-colors duration-200 text-sm">
                                    Get Quote <i class="fas fa-arrow-right ml-2 text-xs"></i>
                                </a>
                            </div>
                        </div>
                    `).join('');

                    productsPreview.innerHTML = html;
                });
            }

            // Delete product
            window.deleteProduct = async function(productId) {
                if (!confirm('Are you sure you want to delete this product?')) {
                    return;
                }

                try {
                    await database.ref(`customProducts/${productId}`).remove();
                    showMessage('success', 'Product deleted successfully!');
                } catch (error) {
                    console.error('Error deleting product:', error);
                    showMessage('error', 'Error deleting product: ' + error.message);
                }
            };

            // Refresh products
            refreshBtn.addEventListener('click', () => {
                loadRecentProducts();
                loadProductsPreview();
            });

            // Initialize
            updateFirebaseStatus('connected', 'Connected');
            loadRecentProducts();
            loadProductsPreview();
        });
    </script>
