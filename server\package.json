{"name": "creative-hydraulics-admin-server", "version": "1.0.0", "description": "Node.js Express server for Creative Hydraulics Admin Panel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "firebase", "admin", "creative-hydraulics"], "author": "Creative Hydraulics", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "firebase-admin": "^12.0.0", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "path": "^0.12.7", "fs": "^0.0.1-security"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}