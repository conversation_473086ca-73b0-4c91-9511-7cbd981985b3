const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Firebase Admin SDK
const admin = require('firebase-admin');

// Initialize Firebase Admin
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID || "chwk-edca8",
  private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
  client_id: process.env.FIREBASE_CLIENT_ID,
  auth_uri: process.env.FIREBASE_AUTH_URI || "https://accounts.google.com/o/oauth2/auth",
  token_uri: process.env.FIREBASE_TOKEN_URI || "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: process.env.FIREBASE_AUTH_PROVIDER_X509_CERT_URL || "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: process.env.FIREBASE_CLIENT_X509_CERT_URL,
  universe_domain: process.env.FIREBASE_UNIVERSE_DOMAIN || "googleapis.com"
};

// Initialize Firebase Admin (fallback to default credentials if service account not provided)
try {
  if (process.env.FIREBASE_PRIVATE_KEY && process.env.FIREBASE_CLIENT_EMAIL) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: process.env.FIREBASE_DATABASE_URL || 'https://chwk-edca8-default-rtdb.firebaseio.com/',
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'chwk-edca8.firebasestorage.app'
    });
  } else {
    // Fallback to default credentials (for development)
    admin.initializeApp({
      databaseURL: process.env.FIREBASE_DATABASE_URL || 'https://chwk-edca8-default-rtdb.firebaseio.com/',
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'chwk-edca8.firebasestorage.app'
    });
  }
  console.log('Firebase Admin initialized successfully');
} catch (error) {
  console.error('Firebase Admin initialization error:', error.message);
  console.log('Continuing with client-side Firebase only...');
}

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://www.gstatic.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https://chwk-edca8-default-rtdb.firebaseio.com", "https://firebasestorage.googleapis.com"]
    }
  }
}));

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = (process.env.ALLOWED_ORIGINS || 'http://localhost:3000').split(',');
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true
};
app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, '../')));
app.use('/admin', express.static(path.join(__dirname, '../admin')));
app.use('/pages', express.static(path.join(__dirname, '../pages')));
app.use('/assets', express.static(path.join(__dirname, '../assets')));

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/jpg,image/png,image/webp').split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images are allowed.'), false);
    }
  }
});

// Routes
app.get('/', (req, res) => {
  res.redirect('/admin');
});

app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/working-admin.html'));
});

app.get('/admin/working', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/working-admin.html'));
});

app.get('/admin/hybrid', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/hybrid-admin.html'));
});

app.get('/admin/server', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/admin.html'));
});

app.get('/admin/simple', (req, res) => {
  res.sendFile(path.join(__dirname, '../admin/simple-admin.html'));
});

app.get('/admin/client', (req, res) => {
  res.sendFile(path.join(__dirname, '../admin/index.html'));
});

// API Routes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    firebase: admin.apps.length > 0 ? 'Connected' : 'Not Connected'
  });
});

// Get all products
app.get('/api/products', async (req, res) => {
  try {
    const db = admin.database();
    const snapshot = await db.ref('customProducts').once('value');
    const products = snapshot.val() || {};
    
    // Convert to array and sort by timestamp
    const productsArray = Object.entries(products)
      .map(([id, product]) => ({ id, ...product }))
      .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
    
    res.json({
      success: true,
      products: productsArray,
      count: productsArray.length
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch products',
      message: error.message
    });
  }
});

// Add new product
app.post('/api/products', upload.single('image'), async (req, res) => {
  try {
    const { name, description } = req.body;
    const file = req.file;

    console.log('Received product data:', { name, description, fileSize: file?.size });

    // Validation
    if (!name || !description || !file) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: name, description, and image'
      });
    }

    let imageUrl;
    let productData;

    // Try Firebase Admin SDK first, fallback to client-side approach
    if (admin.apps.length > 0) {
      try {
        console.log('Using Firebase Admin SDK for upload...');

        // Upload image to Firebase Storage
        const bucket = admin.storage().bucket();
        const fileName = `products/${Date.now()}_${file.originalname.replace(/[^a-zA-Z0-9.]/g, '_')}`;
        const fileUpload = bucket.file(fileName);

        const stream = fileUpload.createWriteStream({
          metadata: {
            contentType: file.mimetype,
          },
        });

        const uploadPromise = new Promise((resolve, reject) => {
          stream.on('error', reject);
          stream.on('finish', async () => {
            try {
              await fileUpload.makePublic();
              const publicUrl = `https://storage.googleapis.com/${bucket.name}/${fileName}`;
              resolve(publicUrl);
            } catch (error) {
              reject(error);
            }
          });
        });

        stream.end(file.buffer);
        imageUrl = await uploadPromise;

        // Save product to Firebase Database
        const db = admin.database();
        const productRef = db.ref('customProducts').push();

        productData = {
          name: name.trim(),
          description: description.trim(),
          imageUrl: imageUrl,
          timestamp: admin.database.ServerValue.TIMESTAMP,
          createdAt: new Date().toISOString(),
          id: productRef.key
        };

        await productRef.set(productData);
        console.log('Product saved via Admin SDK');

      } catch (adminError) {
        console.error('Admin SDK failed, using fallback:', adminError);
        throw adminError;
      }
    } else {
      // Fallback: Convert image to base64 and let client handle Firebase upload
      const base64Image = file.buffer.toString('base64');
      const dataUrl = `data:${file.mimetype};base64,${base64Image}`;

      productData = {
        name: name.trim(),
        description: description.trim(),
        imageData: dataUrl,
        fileName: file.originalname,
        timestamp: Date.now(),
        createdAt: new Date().toISOString(),
        needsUpload: true
      };

      console.log('Returning data for client-side upload');
    }

    res.json({
      success: true,
      message: 'Product processed successfully',
      product: productData,
      imageUrl: imageUrl
    });

  } catch (error) {
    console.error('Error adding product:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add product',
      message: error.message
    });
  }
});

// Delete product
app.delete('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        success: false,
        error: 'Product ID is required'
      });
    }

    const db = admin.database();
    await db.ref(`customProducts/${id}`).remove();

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete product',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large',
        message: 'File size must be less than 10MB'
      });
    }
  }
  
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found',
    message: 'The requested resource was not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Creative Hydraulics Admin Server running on port ${PORT}`);
  console.log(`📱 Admin Panel: http://localhost:${PORT}/admin`);
  console.log(`🔧 Simple Admin: http://localhost:${PORT}/admin/simple`);
  console.log(`⚡ API Health: http://localhost:${PORT}/api/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
