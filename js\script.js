// Hero Slider Configuration
const heroSlides = [
    {
        image: 'https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
        title: 'Leading Manufacturer of <span class="text-blue-400">Hydraulic Equipment</span>',
        description: 'Providing innovative and reliable hydraulic solutions to industries worldwide'
    },
    {
        image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
        title: 'Global Excellence in <span class="text-blue-400">Testing Solutions</span>',
        description: 'Delivering high-quality hydraulic testing equipment across the globe'
    },
    {
        image: 'https://images.unsplash.com/photo-1581092334651-ddf26d9a09d0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80',
        title: 'Innovative Solutions for <span class="text-blue-400">Industry Leaders</span>',
        description: 'Custom-engineered solutions meeting international quality standards'
    }
];

let currentSlide = 0;
const sliderContainer = document.getElementById('hero-slider');
const sliderDots = document.querySelectorAll('.bottom-10 button');
let autoSlideInterval;

// Create HTML for a slide
function createSlideHTML(slide, index) {
    return `
        <div class="absolute inset-0 transition-opacity duration-1000 ${index === 0 ? 'opacity-100' : 'opacity-0'}">
            <div class="absolute inset-0 bg-cover bg-center hero-slide ${index === 0 ? 'active' : ''}" style="background-image: url('${slide.image}');">
                <div class="absolute inset-0 bg-gradient-to-b from-black/60 via-black/50 to-black/70"></div>
            </div>
            <div class="relative h-full flex items-center">
                <div class="container mx-auto px-4">
                    <div class="max-w-4xl mx-auto text-center text-white space-y-7">
                        <h1 class="text-4xl md:text-6xl font-Michroma font-bold animate-fadeInUp opacity-0 leading-tight" style="animation-delay: 0.2s">
                            ${slide.title}
                        </h1>
                        <p class="text-lg md:text-xl font-Jura animate-fadeInUp opacity-0 text-gray-200 max-w-2xl mx-auto leading-relaxed tracking-wide" style="animation-delay: 0.4s">
                            ${slide.description}
                        </p>
                        <div class="flex justify-center space-x-5 animate-fadeInUp opacity-0 pt-4" style="animation-delay: 0.6s">
                            <a href="#contact" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3.5 rounded-lg text-base font-Jura font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg">
                                Contact Us
                            </a>
                            <a href="#products" class="bg-transparent border-2 border-white/80 hover:bg-white hover:text-blue-900 text-white px-8 py-3.5 rounded-lg text-base font-Jura font-medium transition-all duration-300 hover:scale-105">
                                Our Products
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Initialize slider
function initializeSlider() {
    // Create slides
    heroSlides.forEach((slide, index) => {
        const slideElement = document.createElement('div');
        slideElement.innerHTML = createSlideHTML(slide, index);
        sliderContainer.appendChild(slideElement.firstElementChild);
    });

    // Add click events to dots
    sliderDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            clearInterval(autoSlideInterval);
            goToSlide(index);
            startAutoSlide();
        });
    });

    // Start automatic sliding
    startAutoSlide();

    // Pause auto-slide on hover
    sliderContainer.addEventListener('mouseenter', () => {
        clearInterval(autoSlideInterval);
    });

    sliderContainer.addEventListener('mouseleave', () => {
        startAutoSlide();
    });
}

// Go to specific slide
function goToSlide(index) {
    const slides = sliderContainer.children;
    const heroSlides = document.querySelectorAll('.hero-slide');
    
    // Update slides
    Array.from(slides).forEach((slide, i) => {
        slide.style.opacity = i === index ? '1' : '0';
        
        // Reset animations
        const animatedElements = slide.querySelectorAll('.animate-fadeInUp');
        animatedElements.forEach(el => {
            el.style.opacity = '0';
            void el.offsetWidth; // Trigger reflow
            el.style.opacity = i === index ? '1' : '0';
        });
    });

    // Update hero slide zoom effect
    heroSlides.forEach((slide, i) => {
        if (i === index) {
            slide.classList.add('active');
        } else {
            slide.classList.remove('active');
        }
    });

    // Update dots
    sliderDots.forEach((dot, i) => {
        if (i === index) {
            dot.classList.add('opacity-100');
            dot.classList.remove('opacity-50');
        } else {
            dot.classList.remove('opacity-100');
            dot.classList.add('opacity-50');
        }
    });

    currentSlide = index;
}

// Start auto-slide
function startAutoSlide() {
    autoSlideInterval = setInterval(nextSlide, 5000);
}

// Go to next slide
function nextSlide() {
    const nextIndex = (currentSlide + 1) % heroSlides.length;
    goToSlide(nextIndex);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeSlider();

    // Mobile Menu Functionality
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuContent = document.getElementById('mobile-menu-content');
    const mobileMenuBackdrop = document.getElementById('mobile-menu-backdrop');
    const mobileMenuClose = document.getElementById('mobile-menu-close');

    function toggleMobileMenu() {
        mobileMenu.classList.toggle('hidden');
        setTimeout(() => {
            mobileMenuBackdrop.classList.toggle('opacity-0');
            mobileMenuContent.classList.toggle('translate-x-full');
        }, 50);
    }

    mobileMenuButton.addEventListener('click', toggleMobileMenu);
    mobileMenuClose.addEventListener('click', toggleMobileMenu);
    mobileMenuBackdrop.addEventListener('click', toggleMobileMenu);

    // Close mobile menu when clicking on a link
    const mobileMenuLinks = mobileMenu.querySelectorAll('a');
    mobileMenuLinks.forEach(link => {
        link.addEventListener('click', toggleMobileMenu);
    });
}); 